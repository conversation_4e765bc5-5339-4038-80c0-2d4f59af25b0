# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
**/node_modules
**/.pnp
**/.pnp.*
**/.yarn/*
!**/.yarn/patches
!**/.yarn/plugins
!**/.yarn/releases
!**/.yarn/versions

# testing
**/coverage

# package managers
**/yarn.lock
**/package-lock.json
**/.pnpm-lock.yaml

# next.js
**/.next/
**/out/

# production
**/build
**/dist

# misc
.DS_Store
*.pem
.env.local
.env.development.local
.env.test.local
.env.production.local

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# deployment
.vercel
.serverless
.netlify

# typescript
*.tsbuildinfo
**/next-env.d.ts

# IDE
.idea
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
