'use client'

import { Suspense, useState } from 'react'
import AppLayout from '../layout/AppLayout'
import { PageData } from '../../types'
import SectionLoading from '../ui/molecules/SectionLoading'
import ErrorFallback from '../ui/molecules/ErrorFallback'

import { DeviceProvider } from '../../providers/DeviceProvider'
import { DeviceType } from '../../lib/constants/breakpoints'
import ErrorBoundary from '../ui/molecules/ErrorBoundary'

interface HomePageProps {
    pageData: PageData
    initialDeviceType?: DeviceType
}

/**
 * HomePage component
 * Uses PageLayout to render sections
 * Wrapped with DeviceProvider and ErrorBoundary for better user experience
 */
export default function HomePage({
    pageData,
    initialDeviceType = DeviceType.Desktop,
}: HomePageProps) {
    // Both SectionLoading and ErrorFallback are now imported as reusable components

    // Log the page data to verify it's coming from the backend
    console.log(
        'HomePage received pageData:',
        pageData.sections.find((section) => section.type === 'search-service')?.value?.title,
    )

    // State to force re-render on error
    const [key, setKey] = useState(0)

    // Handler for retrying after an error
    const handleRetry = () => {
        // Increment key to force re-render of the entire component tree
        setKey((prevKey) => prevKey + 1)
    }

    return (
        <DeviceProvider initialDeviceType={initialDeviceType} key={key}>
            <ErrorBoundary
                fallback={
                    <ErrorFallback
                        title="Something went wrong"
                        message="We're sorry, but there was an error loading the page. Please try again."
                        // Only pass onRetry function on client-side to prevent SSR serialization issues
                        onRetry={typeof window !== 'undefined' ? handleRetry : undefined}
                    />
                }
                onError={(error) => {
                    // Log error to monitoring service (would be implemented in production)
                    console.error('HomePage error:', error)
                }}
            >
                <div data-testid="home-page">
                    <Suspense fallback={<SectionLoading />}>
                        <AppLayout sections={pageData.sections} />
                    </Suspense>
                </div>
            </ErrorBoundary>
        </DeviceProvider>
    )
}
