import { NextRequest, NextResponse } from 'next/server'

// Define protected routes that require authentication
const PROTECTED_ROUTES = [
    '/dashboard',
    '/profile',
    '/orders',
    '/cart',
    '/checkout',
    '/account',
    '/settings',
]

// Define auth routes that should redirect to home if already authenticated
const AUTH_ROUTES = ['/auth', '/auth/otp-verification']

// Public routes that don't need authentication checks
const PUBLIC_ROUTES = ['/', '/discovery', '/products', '/categories']

/**
 * Next.js middleware for authentication and route protection
 *
 * This middleware:
 * 1. Protects routes that require authentication
 * 2. Redirects authenticated users away from auth pages
 * 3. Handles token validation at the edge
 *
 * @param req - Next.js request object
 * @returns Next.js response
 */
export function middleware(req: NextRequest) {
    const { pathname } = req.nextUrl

    // Check if the user is authenticated by looking for tokens in cookies
    const sessionToken = req.cookies.get('session_token')

    // Also check for isVerified in cookies (fallback for client-side auth)
    const isVerifiedCookie = req.cookies.get('isVerified')

    // Determine authentication status from available sources
    const isAuthenticated = !!sessionToken || isVerifiedCookie?.value === 'true'

    // Log authentication status for debugging (only in development)
    if (process.env.NODE_ENV === 'development') {
        console.log(`[Middleware] Path: ${pathname}`)
        console.log(`[Middleware] Session Token: ${!!sessionToken}`)
        console.log(`[Middleware] Is Verified Cookie: ${isVerifiedCookie?.value}`)
        console.log(`[Middleware] Is Authenticated: ${isAuthenticated}`)
    }

    // Check if this is a protected route
    const isProtectedRoute = PROTECTED_ROUTES.some(
        (route) => pathname === route || pathname.startsWith(`${route}/`),
    )

    // Check if this is an auth route
    const isAuthRoute = AUTH_ROUTES.some(
        (route) => pathname === route || pathname.startsWith(`${route}/`),
    )

    // If it's a protected route and user is not authenticated, redirect to login
    if (isProtectedRoute && !isAuthenticated) {
        const url = new URL('/auth', req.url)
        // Add the original URL as a query parameter to redirect back after login
        url.searchParams.set('callbackUrl', pathname)
        console.log(`[Middleware] Redirecting to: ${url.toString()}`)
        return NextResponse.redirect(url)
    }

    // If it's an auth route and user is already authenticated, redirect to discovery
    if (isAuthRoute && isAuthenticated) {
        const redirectUrl = new URL('/discovery', req.url)
        console.log(`[Middleware] Redirecting authenticated user to: ${redirectUrl.toString()}`)
        return NextResponse.redirect(redirectUrl)
    }

    // For all other routes, continue normally
    return NextResponse.next()
}

// Configure middleware to run only on specific paths
export const config = {
    matcher: [
        /*
         * Match all request paths except:
         * - _next/static (static files)
         * - _next/image (image optimization files)
         * - favicon.ico (favicon file)
         * - public files (public folder)
         * - api routes (/api/*)
         */
        '/((?!_next/static|_next/image|favicon.ico|public|api).*)',
    ],
}
