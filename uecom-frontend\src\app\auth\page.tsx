'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import Head from 'next/head'
import PhoneInput from '@/components/PhoneInput'
import { requestOTP } from '../api/auth'

function SignInComponent() {
    const [phone, setPhone] = useState<string | undefined>(undefined)
    const router = useRouter()

    useEffect(() => {
        const phoneNumber = localStorage.getItem('phone') || undefined
        setPhone(phoneNumber)

        if (localStorage.getItem('isVerified') === 'true') {
            router.push('/discovery')
        }
    }, [])

    const handleSendOTP = async () => {
        if (!phone || phone.toString().length !== 10) {
            alert('Please enter a valid 10-digit phone number.')
            return
        }

        try {
            if (!phone) {
                throw new Error('Phone number is not valid')
            }
            const data = await requestOTP(phone)
            localStorage.setItem('phone', phone.toString())
            localStorage.setItem('dialCode', '+91')
            console.log('OTP sent successfully:', data)
            router.push('/auth/otp-verification')
        } catch (error: any) {
            console.error('Error sending OTP:', error.message)
            alert(error.message || 'Something went wrong. Please try again.')
        }
    }

    return (
        <>
            <Head>
                <title>Sign In | My eCommerce</title>
                <meta
                    name="description"
                    content="Sign in with your phone number to access your account."
                />
            </Head>

            <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100 px-4">
                <h2 className="text-2xl font-bold text-gray-900">Continue With Phone</h2>
                <p className="text-gray-500 text-sm mt-1">We will send OTP</p>

                <div className="mt-8 w-32 h-32 rounded-full overflow-hidden border border-gray-300 shadow-lg flex items-center justify-center">
                    <Image
                        src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSq5V8lnoXir1THjSRVRMPuc1kqHginjO9rkA&s"
                        width={100}
                        height={100}
                        alt="Phone Authentication"
                        className="object-cover"
                        loading="lazy"
                    />
                </div>

                <div className="mt-8 w-full max-w-sm">
                    <label className="block text-blue-primary text-sm font-medium mb-2">
                        Enter your phone number <span className="text-red-500">*</span>
                    </label>
                    <PhoneInput value={phone} onChange={setPhone} />
                </div>

                <button
                    onClick={handleSendOTP}
                    disabled={phone?.toString().length !== 10}
                    className="mt-6 bg-[#162D50] text-white font-semibold py-3 rounded-lg w-full max-w-sm shadow-md
                                hover:bg-[#0F1E3C] transition duration-300
                                disabled:bg-blue-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:opacity-50"
                >
                    Get OTP
                </button>
            </div>
        </>
    )
}

// Use dynamic import to ensure client-side only rendering
import dynamic from 'next/dynamic'

const SignIn = dynamic(() => Promise.resolve(SignInComponent), {
    ssr: false,
    loading: () => (
        <div className="flex items-center justify-center min-h-screen bg-gray-100">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
    ),
})

export default SignIn
