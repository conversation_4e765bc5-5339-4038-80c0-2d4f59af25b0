/**
 * Authentication Controller
 *
 * This controller handles HTTP requests related to authentication operations,
 * including health checks, OTP requests, and OTP verification.
 *
 * @module controllers/auth
 */

import { Request, Response } from 'express';
import { buildAPIResponse } from '../utils/apiResponse';
import { StatusCodes } from 'http-status-codes';
import { ServiceFactory } from '../factories/service-factory';
import { jwtService } from '../services/jwt.service';
import { cookieService } from '../services/cookie.service';
import { databaseService } from '../services/database.service';
import { logger } from '../utils/logger';
import { ConfigService } from '../configs/config.service';

/**
 * Health check endpoint to verify service status
 *
 * @param {Request} _req - Express request object (unused)
 * @param {Response} res - Express response object
 * @returns {void}
 */
export const healthCheck = (_req: Request, res: Response): void => {
    // Get the auth service from the factory
    const authService = ServiceFactory.getAuthService();

    const response = buildAPIResponse(
        StatusCodes.OK,
        'Service is up and running',
        authService.healthCheck()
    );
    res.status(StatusCodes.OK).json(response);
};

/**
 * Requests an OTP to be sent to the provided mobile number
 *
 * @param {Request} req - Express request object containing mobile number
 * @param {Response} res - Express response object
 * @returns {Promise<void>}
 */
export const requestOtp = async (
    req: Request,
    res: Response
): Promise<void> => {
    // Extract mobile number from request body
    const { mobile } = req.body;

    // Get the auth service from the factory
    const authService = ServiceFactory.getAuthService();

    // Call the auth service to generate and send OTP
    const serviceResponse = await authService.requestOtp({ mobile });

    // Handle error case
    if (!serviceResponse.success) {
        const response = buildAPIResponse(
            StatusCodes.INTERNAL_SERVER_ERROR,
            serviceResponse.error?.message || 'Failed to send OTP',
            null,
            serviceResponse.error
        );
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json(response);
        return;
    }

    // Handle success case
    const response = buildAPIResponse(
        StatusCodes.OK,
        'OTP sent successfully',
        serviceResponse.data
    );
    res.status(StatusCodes.OK).json(response);
};

/**
 * Verifies an OTP submitted by the user and generates JWT tokens on success
 *
 * @param {Request} req - Express request object containing mobile number and OTP
 * @param {Response} res - Express response object
 * @returns {Promise<void>}
 */
export const verifyOtp = async (req: Request, res: Response): Promise<void> => {
    // Extract mobile number and OTP from request body
    const { mobile, otp } = req.body;

    // Get the auth service from the factory
    const authService = ServiceFactory.getAuthService();

    try {
        // Call the auth service to verify the OTP
        const serviceResponse = await authService.verifyOtp({ mobile, otp });

        // Handle error case
        if (!serviceResponse.success) {
            const response = buildAPIResponse(
                StatusCodes.UNAUTHORIZED,
                serviceResponse.error?.message || 'OTP verification failed',
                null,
                serviceResponse.error
            );
            res.status(StatusCodes.UNAUTHORIZED).json(response);
            return;
        }

        try {
            // Get client information
            const clientIp = req.ip || req.socket.remoteAddress || '';
            const userAgent = req.headers['user-agent'] || '';

            // Find or create user in the database
            logger.info(`Finding or creating user with mobile: ${mobile}`);
            let user;

            try {
                // Try multiple times to handle potential race conditions
                for (let attempt = 1; attempt <= 3; attempt++) {
                    try {
                        logger.info(
                            `Attempt ${attempt} to find or create user with mobile: ${mobile}`
                        );

                        // First try to find the user directly
                        user = await databaseService.findUserByMobile(mobile);

                        if (user) {
                            logger.info(
                                `Found existing user with mobile: ${mobile} on attempt ${attempt}`
                            );
                            break; // Exit the loop if user is found
                        }

                        // If not found, try to create the user
                        user = await databaseService.findOrCreateUser(
                            mobile,
                            clientIp,
                            userAgent
                        );

                        if (user) {
                            logger.info(
                                `Successfully created or found user with mobile: ${mobile} on attempt ${attempt}`
                            );
                            break; // Exit the loop if user is created/found
                        }

                        // If we get here, user is still null, so we'll retry
                        logger.warn(
                            `Attempt ${attempt} failed to find or create user with mobile: ${mobile}`
                        );

                        // Small delay before retrying
                        if (attempt < 3) {
                            await new Promise((resolve) =>
                                setTimeout(resolve, 500)
                            );
                        }
                    } catch (attemptError) {
                        logger.error(
                            `Database error on attempt ${attempt} while processing user with mobile: ${mobile}`,
                            attemptError
                        );

                        // If this is the last attempt, rethrow the error
                        if (attempt === 3) {
                            throw attemptError;
                        }

                        // Small delay before retrying
                        await new Promise((resolve) =>
                            setTimeout(resolve, 500)
                        );
                    }
                }

                // Check if we have a user after all attempts
                if (!user) {
                    logger.error(
                        `Failed to find or create user with mobile: ${mobile} after multiple attempts`
                    );
                    const response = buildAPIResponse(
                        StatusCodes.INTERNAL_SERVER_ERROR,
                        'Failed to process user data',
                        null,
                        { code: 'USER_PROCESSING_FAILED' }
                    );
                    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json(
                        response
                    );
                    return;
                }
            } catch (dbError) {
                logger.error(
                    `Unhandled database error while processing user with mobile: ${mobile}`,
                    dbError
                );
                const response = buildAPIResponse(
                    StatusCodes.INTERNAL_SERVER_ERROR,
                    'Database error while processing user data',
                    null,
                    { code: 'DATABASE_ERROR' }
                );
                res.status(StatusCodes.INTERNAL_SERVER_ERROR).json(response);
                return;
            }

            // Get the user ID from the database
            const userId = user.consumer_id;

            try {
                // Get or create the MOBILE identity type
                const identityType =
                    await databaseService.getOrCreateIdentityType('MOBILE');

                if (!identityType) {
                    logger.warn(
                        'Could not get or create MOBILE identity type, continuing without identity linking'
                    );
                } else {
                    // Link the mobile identity to the user if not already linked
                    await databaseService.linkUserIdentity(
                        userId,
                        identityType.identityId,
                        mobile,
                        clientIp,
                        userAgent
                    );
                }

                // Record the login session
                await databaseService.recordLogin(
                    userId,
                    'MOBILE',
                    mobile,
                    clientIp,
                    userAgent
                );
            } catch (dbError) {
                // Log the error but continue with authentication
                // This ensures the user can still log in even if identity linking fails
                logger.error(
                    'Error during identity or login record operations:',
                    dbError
                );
            }

            // Generate JWT tokens
            const accessToken = jwtService.generateAccessToken(userId);
            const refreshToken = jwtService.generateRefreshToken(userId);
            const sessionToken = jwtService.generateSessionToken(userId);

            // Set secure HTTP-only cookie with session token
            cookieService.setSessionCookie(res, sessionToken);

            // Calculate token expiry in seconds for client-side use
            const expiryHours = new ConfigService().getJwtSessionExpiryHours();
            const expirySeconds = expiryHours * 60 * 60;

            // Handle success case with tokens
            const response = buildAPIResponse(
                StatusCodes.OK,
                'OTP verified successfully',
                {
                    ...serviceResponse.data,
                    tokens: {
                        accessToken,
                        refreshToken,
                        expiresIn: expirySeconds,
                    },
                    user: {
                        id: userId,
                        mobile,
                    },
                    redirectTo: '/discovery', // Redirect URL as per requirements
                }
            );

            // Set secure HTTP-only cookie with refresh token (for backward compatibility)
            if (process.env.NODE_ENV === 'production') {
                const refreshCookieOptions = {
                    httpOnly: true,
                    secure: true, // Only sent over HTTPS
                    sameSite: 'strict' as const,
                    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
                    path: '/v1/auth/refresh-token',
                };

                res.cookie('refreshToken', refreshToken, refreshCookieOptions);

                logger.debug(
                    'Setting refresh token cookie with options:',
                    refreshCookieOptions
                );
            }

            logger.info(`User ${userId} successfully authenticated`);
            res.status(StatusCodes.OK).json(response);
        } catch (error) {
            logger.error('Error in OTP verification process:', error);

            const response = buildAPIResponse(
                StatusCodes.INTERNAL_SERVER_ERROR,
                'Authentication successful but processing failed',
                null,
                { code: 'AUTHENTICATION_PROCESSING_FAILED' }
            );
            res.status(StatusCodes.INTERNAL_SERVER_ERROR).json(response);
        }
    } catch (error) {
        logger.error('Error verifying OTP:', error);

        const response = buildAPIResponse(
            StatusCodes.INTERNAL_SERVER_ERROR,
            'Error processing OTP verification',
            null,
            { code: 'OTP_VERIFICATION_ERROR' }
        );
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json(response);
    }
};

/**
 * Refreshes an access token using a refresh token
 *
 * @param {Request} req - Express request object containing refresh token
 * @param {Response} res - Express response object
 * @returns {Promise<void>}
 */
export const refreshToken = async (
    req: Request,
    res: Response
): Promise<void> => {
    try {
        // Get refresh token from request body or cookie
        const refreshToken = req.body.refreshToken || req.cookies?.refreshToken;

        if (!refreshToken) {
            const response = buildAPIResponse(
                StatusCodes.BAD_REQUEST,
                'Refresh token is required',
                null,
                { code: 'REFRESH_TOKEN_REQUIRED' }
            );
            res.status(StatusCodes.BAD_REQUEST).json(response);
            return;
        }

        // Verify the refresh token
        const decoded = jwtService.verifyToken(refreshToken);

        // Check if it's a refresh token
        if (decoded.type !== 'refresh') {
            const response = buildAPIResponse(
                StatusCodes.UNAUTHORIZED,
                'Invalid token type',
                null,
                { code: 'INVALID_TOKEN_TYPE' }
            );
            res.status(StatusCodes.UNAUTHORIZED).json(response);
            return;
        }

        // Generate a new access token
        const userId = decoded.sub;
        const newAccessToken = jwtService.generateAccessToken(userId);

        // Return the new access token
        const response = buildAPIResponse(
            StatusCodes.OK,
            'Token refreshed successfully',
            {
                accessToken: newAccessToken,
                expiresIn: 3600, // 1 hour in seconds
            }
        );
        res.status(StatusCodes.OK).json(response);
    } catch (error: any) {
        // Handle token verification errors
        let statusCode = StatusCodes.UNAUTHORIZED;
        let message = 'Invalid refresh token';
        let code = 'INVALID_REFRESH_TOKEN';

        if (error.message === 'Token has expired') {
            message = 'Refresh token has expired';
            code = 'REFRESH_TOKEN_EXPIRED';
        }

        logger.warn('Refresh token error:', {
            message,
            code,
            error: error.message,
        });
        const response = buildAPIResponse(statusCode, message, null, { code });
        res.status(statusCode).json(response);
    }
};

/**
 * Logs out the user by clearing all authentication cookies
 *
 * @param {Request} _req - Express request object (unused)
 * @param {Response} res - Express response object
 * @returns {void}
 */
export const logout = (_req: Request, res: Response): void => {
    try {
        // Clear the session cookie
        cookieService.clearSessionCookie(res);

        // Clear the refresh token cookie (for backward compatibility)
        res.clearCookie('refreshToken', {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'strict',
            path: '/v1/auth/refresh-token',
        });

        // Return success response
        const response = buildAPIResponse(
            StatusCodes.OK,
            'Logged out successfully',
            null
        );
        res.status(StatusCodes.OK).json(response);
    } catch (error) {
        logger.error('Error during logout:', error);

        const response = buildAPIResponse(
            StatusCodes.INTERNAL_SERVER_ERROR,
            'Error processing logout',
            null,
            { code: 'LOGOUT_ERROR' }
        );
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json(response);
    }
};
