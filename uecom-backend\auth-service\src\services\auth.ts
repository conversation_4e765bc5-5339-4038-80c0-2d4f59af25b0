/**
 * Authentication Service Implementation
 *
 * This service handles authentication operations using different strategies.
 * It follows the Strategy pattern to allow for different authentication methods.
 *
 * It also follows the Dependency Inversion Principle by depending on abstractions
 * rather than concrete implementations.
 *
 * @module services/auth
 */

import { IAuthService } from '../interfaces/auth-service.interface';
import { IAuthStrategy } from '../interfaces/auth-strategy.interface';
import {
    OtpRequest,
    OtpVerificationRequest,
    ServiceResponse,
    OtpServiceResponse,
    VerificationResponse,
} from '../types/auth.types';

export class AuthService implements IAuthService {
    /**
     * Creates a new instance of the AuthService
     *
     * @param {IAuthStrategy} authStrategy - The authentication strategy to use
     */
    constructor(private readonly authStrategy: IAuthStrategy) {}

    /**
     * Health check method to verify service status
     * @returns {string} Status message
     */
    healthCheck(): string {
        return 'OK';
    }

    /**
     * Initiates the authentication process using the current strategy
     *
     * @param {OtpRequest} params - Object containing authentication parameters
     * @returns {Promise<ServiceResponse<OtpServiceResponse>>} Response with success status and data or error
     */
    async requestOtp(
        params: OtpRequest
    ): Promise<ServiceResponse<OtpServiceResponse>> {
        return await this.authStrategy.initiate(params);
    }

    /**
     * Verifies the authentication using the current strategy
     *
     * @param {OtpVerificationRequest} params - Object containing verification parameters
     * @returns {Promise<ServiceResponse<VerificationResponse>>} Response with verification result
     */
    async verifyOtp(
        params: OtpVerificationRequest
    ): Promise<ServiceResponse<VerificationResponse>> {
        return await this.authStrategy.verify(params);
    }
}

// Singleton instance for backward compatibility
// This is used by the tests
let _authServiceInstance: AuthService | null = null;

// Function to get the singleton instance
export function getAuthServiceInstance(): AuthService {
    if (!_authServiceInstance) {
        throw new Error('Auth service not initialized');
    }
    return _authServiceInstance;
}

// Function to set the singleton instance
export function setAuthServiceInstance(instance: AuthService): void {
    _authServiceInstance = instance;
}

// Export a getter for backward compatibility
export const authService = {
    get instance(): AuthService {
        return getAuthServiceInstance();
    },
};
