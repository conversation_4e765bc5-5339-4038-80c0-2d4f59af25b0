import axios, { AxiosError } from 'axios'

class CustomError extends Error {
    constructor(message: string) {
        super(message)
        this.name = this.constructor.name
    }
}

class UnexpectedError extends CustomError {
    constructor(message: string = 'An unexpected error occurred') {
        super(message)
    }
}

class NetworkError extends CustomError {
    constructor(message: string = 'A network error occurred') {
        super(message)
    }
}

class ClientError extends CustomError {
    constructor(message: string = 'A client error occurred') {
        super(message)
    }
}

class ServerError extends CustomError {
    constructor(message: string = 'A server error occurred') {
        super(message)
    }
}

const logError = (error: Error, url: string, additionalData: any = {}) => {
    console.error(`Error encountered at ${url}:`, error)

    console.error('Error Details:', {
        name: error.name,
        message: error.message,
        stack: error.stack,
        ...additionalData,
    })
}

export const handleSuccess = <T>(response: T): T => {
    return response
}

export const handleClientError = (error: AxiosError | any): void => {
    const message = error.response?.data?.message || 'Client error occurred'
    logError(new ClientError(message), error.config.url, { response: error.response })
    throw new ClientError(message)
}

export const handleServerError = (error: AxiosError | any): void => {
    const message = error.response?.data?.message || 'Server error occurred'
    logError(new ServerError(message), error.config.url, { response: error.response })
    throw new ServerError(message)
}

export const handleNetworkError = (error: AxiosError): void => {
    const message = error.message || 'A network error occurred'

    logError(new NetworkError(message), error.config?.url ?? 'Unknown URL', { error })

    throw new NetworkError(message)
}

export const handleError = (error: unknown, url: string): never => {
    if (axios.isAxiosError(error)) {
        const status = error.response?.status
        if (status) {
            if (status >= 400 && status < 500) {
                handleClientError(error)
            } else if (status >= 500 && status < 600) {
                handleServerError(error)
            }
        }

        const message = error.message || 'An unknown Axios error occurred'
        logError(new UnexpectedError(message), url, { error })
        throw new UnexpectedError(`Axios error occurred when calling ${url}: ${message}`)
    }

    if (error instanceof Error) {
        const message = error.message || 'An unknown error occurred'
        logError(new NetworkError(message), url, { error })
        throw new NetworkError(
            `Network error or unexpected issue occurred when calling ${url}: ${message}`,
        )
    }

    const message = 'Unexpected error occurred'
    logError(new UnexpectedError(message), url, { error })
    throw new UnexpectedError(`Unexpected error occurred when calling ${url}`)
}
