import request from 'supertest';
import express from 'express';
import appRouter from '../../src/routes/app';
import { greet } from '../../src/controllers/app';

jest.mock('../../src/controllers/app');

describe('App Routes', () => {
    let app: express.Application;

    beforeEach(() => {
        app = express();
        app.use(express.json());
        app.use('/api', appRouter);
        (greet as jest.Mock).mockImplementation((_req, res) => {
            res.status(200).json({ message: 'Test greeting' });
        });
    });

    describe('GET /api/greet', () => {
        it('should respond to the GET method', async () => {
            const response = await request(app).get('/api/greet');
            expect(response.status).toBe(200);
        });

        it('should return the correct response body', async () => {
            const response = await request(app).get('/api/greet');
            expect(response.body).toHaveProperty('message');
            expect(response.body.message).toBe('Test greeting');
        });

        it('should call the greet controller', async () => {
            await request(app).get('/api/greet');
            expect(greet).toHaveBeenCalled();
        });

        it('should handle invalid path', async () => {
            const response = await request(app).get('/api/invalid');
            expect(response.status).toBe(404);
        });
    });
});
