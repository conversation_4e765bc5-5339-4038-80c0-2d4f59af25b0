{"name": "app-service", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "jest", "dev": "nodemon src/server.ts", "prettier:check": "prettier . --check", "prettier:write": "prettier . --write", "prisma:migrate-dev": "npx prisma migrate dev", "prisma:generate": "npx prisma generate", "prisma:format": "npx prisma format", "prisma:migrate-deploy": "npx prisma migrate deploy"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "devDependencies": {"@types/axios": "^0.14.4", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jest": "^29.5.14", "@types/node": "^22.14.1", "@types/supertest": "^6.0.3", "express": "^5.1.0", "jest": "^29.7.0", "nodemon": "^3.1.9", "prettier": "^3.5.3", "supertest": "^7.1.0", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"@prisma/client": "^6.6.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "prisma": "^6.6.0"}}