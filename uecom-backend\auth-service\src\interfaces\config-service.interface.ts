/**
 * Config Service Interface
 *
 * This interface defines the contract for any configuration service implementation.
 * It follows the Interface Segregation Principle by defining only the methods
 * needed for configuration.
 *
 * @module interfaces/config-service
 */

export interface IConfigService {
    /**
     * Gets the port number for the server
     *
     * @returns {number} The port number
     */
    getPort(): number;

    /**
     * Gets the database URL
     *
     * @returns {string} The database URL
     */
    getDatabaseUrl(): string;

    /**
     * Gets the JWT secret
     *
     * @returns {string} The JWT secret
     */
    getJwtSecret(): string;

    /**
     * Gets the JWT session token expiration time in hours
     *
     * @returns {number} The JWT session token expiration time in hours
     */
    getJwtSessionExpiryHours(): number;

    /**
     * Gets the current Node environment
     *
     * @returns {string} The Node environment
     */
    getNodeEnv(): string;

    /**
     * Gets the MTalkz API key
     *
     * @returns {string} The MTalkz API key
     */
    getMtalkzApiKey(): string;

    /**
     * Gets the MTalkz sender ID
     *
     * @returns {string} The MTalkz sender ID
     */
    getMtalkzSenderId(): string;

    /**
     * Gets the message base URL
     *
     * @returns {string} The message base URL
     */
    getMessageBaseUrl(): string;

    /**
     * Gets the message sub-path
     *
     * @returns {string} The message sub-path
     */
    getMessageSubPath(): string;

    /**
     * Gets the allowed origins for CORS
     *
     * @returns {string[]} The allowed origins
     */
    getAllowedOrigins(): string[];

    /**
     * Gets the Redis URL
     *
     * @returns {string} The Redis URL
     */
    getRedisUrl(): string;

    /**
     * Gets the Redis prefix for OTP keys
     *
     * @returns {string} The Redis prefix for OTP keys
     */
    getRedisOtpPrefix(): string;

    /**
     * Gets the default OTP expiration time in seconds
     *
     * @returns {number} The default OTP expiration time in seconds
     */
    getOtpExpirationSeconds(): number;
}
