import React from 'react'
import { ReviewListSection } from '../../types'
import { Star } from 'lucide-react'
import { useDeviceContext } from '../../providers/DeviceProvider'

interface ReviewListProps {
    data: ReviewListSection
}

const ReviewList: React.FC<ReviewListProps> = ({ data }) => {
    const { label, value } = data
    const { reviews } = value
    const { isMobile, isTablet, isMounted } = useDeviceContext()

    if (!reviews || reviews.length === 0) {
        return null
    }

    const renderStars = (rating: number) => {
        return (
            <div className="flex items-center">
                <span className="text-lg font-medium mr-1">{rating}</span>
                <Star size={16} className="text-yellow-400 fill-yellow-400" />
            </div>
        )
    }

    const getRatingBgColor = (rating: number) => {
        if (rating >= 4) return 'bg-green-100'
        if (rating >= 3) return 'bg-yellow-100'
        return 'bg-red-100'
    }

    const MobileView = () => (
        <section className="py-8 bg-white block sm:block md:hidden">
            <div className="container mx-auto px-4">
                <h2 className="text-xl font-semibold text-gray-800 mb-6">Reviews</h2>

                <div className="space-y-6">
                    {reviews.map((review) => (
                        <div key={review.author}>
                            <div className="border-b pb-3">
                                <div className="flex items-center justify-between mb-1 pr-2">
                                    <p className="text-gray-700 mb-2">{review.text}</p>
                                    <div
                                        className={`rounded-lg px-3 py-1 ${getRatingBgColor(
                                            review.rating,
                                        )}`}
                                    >
                                        {renderStars(review.rating)}
                                    </div>
                                </div>
                                <div className="flex items-center text-sm text-gray-500">
                                    <span className="font-medium">-{review.author}</span>
                                    <span className="mx-2">|</span>
                                    <span>{review.date}</span>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </section>
    )

    const TabletView = () => (
        <section className="py-8 bg-white hidden md:block lg:hidden">
            <div className="container mx-auto px-5">
                <h2 className="text-xl font-semibold text-gray-800 mb-6">Reviews</h2>
                <div className="grid grid-cols-2 gap-4">
                    {reviews.map((review) => (
                        <div key={review.author} className="border rounded-lg p-4 shadow-sm">
                            <div className="flex justify-between items-start mb-3">
                                <p className="text-gray-700 text-sm h-20 overflow-hidden">
                                    "{review.text}"
                                </p>
                                <div
                                    className={`rounded-lg px-2 py-1 ml-2 ${getRatingBgColor(
                                        review.rating,
                                    )}`}
                                >
                                    {renderStars(review.rating)}
                                </div>
                            </div>
                            <div className="flex items-center text-xs text-gray-500">
                                <span className="font-medium">-{review.author}</span>
                                <span className="mx-1">|</span>
                                <span>{review.date}</span>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </section>
    )

    const DesktopView = () => (
        <section className="py-8 mt-10 px-10 bg-white hidden lg:block">
            <div className="container mx-auto px-4">
                <h2 className="text-xl font-semibold text-gray-800 mb-6">Reviews</h2>
                <div className="grid grid-cols-4 gap-4">
                    {reviews.map((review) => (
                        <div key={review.author} className="border rounded-lg p-4 shadow-sm">
                            <p className="text-gray-700 mb-4 text-sm h-24 overflow-hidden">
                                "{review.text}"
                            </p>
                            <div className="flex items-center text-xs text-gray-500">
                                <span className="font-medium">-{review.author}</span>
                                <span className="mx-1">|</span>
                                <span>{review.date}</span>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </section>
    )

    if (!isMounted) return null
    if (isMobile) return <MobileView />
    if (isTablet) return <TabletView />
    return <DesktopView />
}

export default ReviewList
