/**
 * Breakpoints for responsive design
 * These values should match the Tailwind CSS configuration
 */
export const BREAKPOINTS = {
    xs: 0,
    sm: 640,
    md: 768,
    lg: 1024,
    xl: 1280,
    '2xl': 1536,
} as const

/**
 * Device type enum for consistent device type references
 */
export enum DeviceType {
    Mobile = 'mobile',
    Tablet = 'tablet',
    Desktop = 'desktop',
}

/**
 * Simplified breakpoints for device detection
 * Used for device type detection in both client and server
 */
export const DEVICE_BREAKPOINTS = {
    mobile: BREAKPOINTS.md - 1, // Mobile: 0-767px
    tablet: BREAKPOINTS.lg - 1, // Tablet: 768-1023px
    desktop: BREAKPOINTS.lg, // Desktop: 1024px+
} as const

/**
 * Media queries for responsive design
 * These can be used with CSS-in-JS libraries or for manual media query handling
 */
export const MEDIA_QUERIES = {
    xs: `(min-width: ${BREAKPOINTS.xs}px)`,
    sm: `(min-width: ${BREAKPOINTS.sm}px)`,
    md: `(min-width: ${BREAKPOINTS.md}px)`,
    lg: `(min-width: ${BREAKPOINTS.lg}px)`,
    xl: `(min-width: ${BREAKPOINTS.xl}px)`,
    '2xl': `(min-width: ${BREAKPOINTS['2xl']}px)`,

    // Max-width queries
    xsMax: `(max-width: ${BREAKPOINTS.sm - 1}px)`,
    smMax: `(max-width: ${BREAKPOINTS.md - 1}px)`,
    mdMax: `(max-width: ${BREAKPOINTS.lg - 1}px)`,
    lgMax: `(max-width: ${BREAKPOINTS.xl - 1}px)`,
    xlMax: `(max-width: ${BREAKPOINTS['2xl'] - 1}px)`,

    // Range queries
    smOnly: `(min-width: ${BREAKPOINTS.sm}px) and (max-width: ${BREAKPOINTS.md - 1}px)`,
    mdOnly: `(min-width: ${BREAKPOINTS.md}px) and (max-width: ${BREAKPOINTS.lg - 1}px)`,
    lgOnly: `(min-width: ${BREAKPOINTS.lg}px) and (max-width: ${BREAKPOINTS.xl - 1}px)`,
    xlOnly: `(min-width: ${BREAKPOINTS.xl}px) and (max-width: ${BREAKPOINTS['2xl'] - 1}px)`,

    // Device type queries
    mobile: `(max-width: ${DEVICE_BREAKPOINTS.mobile}px)`,
    tablet: `(min-width: ${DEVICE_BREAKPOINTS.mobile + 1}px) and (max-width: ${DEVICE_BREAKPOINTS.tablet}px)`,
    desktop: `(min-width: ${DEVICE_BREAKPOINTS.desktop}px)`,
} as const
