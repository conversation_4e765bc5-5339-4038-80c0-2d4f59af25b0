{"name": "uecom-backend-node", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "jest", "dev": "nodemon src/server.ts", "prettier:check": "prettier . --check", "prettier:write": "prettier . --write", "prisma:migrate-dev": "npx prisma migrate dev", "prisma:generate": "npx prisma generate", "prisma:format": "npx prisma format", "prisma:migrate-deploy": "npx prisma migrate deploy"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "devDependencies": {"@types/axios": "^0.14.4", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jest": "^29.5.14", "@types/mocha": "^10.0.10", "@types/node": "^22.14.1", "@types/supertest": "^6.0.3", "axios-mock-adapter": "^2.1.0", "express": "^5.1.0", "jest": "^29.7.0", "nodemon": "^3.1.9", "prettier": "^3.5.3", "supertest": "^7.1.0", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"@prisma/client": "^6.8.2", "@types/cookie-parser": "^1.4.8", "@types/jsonwebtoken": "^9.0.9", "@types/uuid": "^9.0.8", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "http-status-codes": "^2.3.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "redis": "^4.7.0", "uuid": "^9.0.1"}}