import React from 'react'
import { StatsSection } from '../../types'
import AnimatedStats from '../client/AnimatedStats'

interface StatsProps {
    data: StatsSection
}

// This is a server component that renders the AnimatedStats client component
const Stats: React.FC<StatsProps> = ({ data }) => {
    // Server-side validation
    if (!data?.value?.stats?.length) {
        return null
    }

    // Render the client component with the data
    return <AnimatedStats data={data} />
}

export default Stats
