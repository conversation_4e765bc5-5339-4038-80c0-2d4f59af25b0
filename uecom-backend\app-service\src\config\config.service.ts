export class ConfigService {
    getPort(): number {
        return Number(process.env.PORT || 8000);
    }

    getDatabaseUrl(): string {
        if (!process.env.DATABASE_URL) {
            throw new Error('DATABASE_URL not set in environment');
        }
        return process.env.DATABASE_URL;
    }

    getJwtSecret(): string {
        if (!process.env.JWT_SECRET) {
            throw new Error('JWT_SECRET not set in environment');
        }
        return process.env.JWT_SECRET;
    }

    /**
     * Gets the brands API URL
     *
     * @returns {string} The brands API URL
     */
    getBrandsApiUrl(): string {
        return process.env.BRANDS_API_URL || '';
    }

    /**
     * Gets the brands API key
     *
     * @returns {string} The brands API key
     */
    getBrandsApiKey(): string {
        return process.env.BRANDS_API_KEY || '';
    }

    /**
     * Gets the brands cache TTL in seconds
     *
     * @returns {number} The brands cache TTL in seconds
     */
    getBrandsCacheTtl(): number {
        return Number(process.env.BRANDS_CACHE_TTL || 3600); // Default: 1 hour
    }
}

export const configService = new ConfigService();
