/**
 * Request Logger Middleware
 *
 * This middleware logs information about incoming requests and their responses.
 * It follows the Decorator pattern by adding logging functionality to the request handling.
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

/**
 * Logs details about incoming requests and their responses
 *
 * @param req - Express request object
 * @param res - Express response object
 * @param next - Express next function
 */
export const requestLogger = (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    // Get the current timestamp when the request starts
    const startTime = Date.now();

    // Log the incoming request
    logger.debug(`🔹 ${req.method} ${req.originalUrl} - Request received`);

    // Log request body if present (but sanitize sensitive data)
    if (req.body && Object.keys(req.body).length > 0) {
        // Create a sanitized copy of the request body
        const sanitizedBody = { ...req.body };

        // Sanitize sensitive fields if they exist
        if (sanitizedBody.password) sanitizedBody.password = '********';
        if (sanitizedBody.token) sanitizedBody.token = '********';
        if (sanitizedBody.otp) sanitizedBody.otp = '******';

        logger.debug(`📦 Request Body:`, sanitizedBody);
    }

    // Log response on finish
    res.on('finish', () => {
        // Calculate request duration
        const duration = Date.now() - startTime;

        // Log the response details
        logger.debug(
            `🔸 ${req.method} ${req.originalUrl} - Status: ${res.statusCode} - Duration: ${duration}ms`
        );
    });

    // Continue to the next middleware
    next();
};
