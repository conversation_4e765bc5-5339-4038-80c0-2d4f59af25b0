'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/stores/auth/auth-store.enhanced'

// Define user type for email/password auth
export interface User {
    id: string
    name: string
    email: string
    role: 'user' | 'admin'
}

// Define auth state for email/password auth
export interface AuthState {
    user: User | null
    isLoading: boolean
    isAuthenticated: boolean
    error: string | null
}

/**
 * Unified authentication hook that supports both:
 * 1. OTP-based authentication using Zustand store
 * 2. Email/password authentication using local state
 *
 * This hook provides a simplified interface to authentication
 * with additional functionality like automatic initialization and navigation helpers.
 *
 * @returns Auth state and functions for both authentication methods
 *
 * @example
 * ```tsx
 * // OTP-based authentication
 * const { mobile, requestOtp, verifyOtp } = useAuth();
 *
 * // Email/password authentication
 * const { user, login, register } = useAuth();
 * ```
 */
export function useAuth() {
    const router = useRouter()
    const [isReady, setIsReady] = useState(false)

    // Local state for email/password auth
    const [authState, setAuthState] = useState<AuthState>({
        user: null,
        isLoading: false,
        isAuthenticated: false,
        error: null,
    })

    // Get state and actions from the auth store for OTP-based auth
    const {
        isInitialized,
        isLoading: storeIsLoading,
        mobile,
        user: storeUser,
        tokens,
        error: storeError,
        initialize,
        checkAuthentication,
        setMobile,
        requestOtp,
        verifyOtp,
        resendOtp,
        logout: storeLogout,
        clearError: storeClearError,
        refreshTokens,
        updateTokens,
    } = useAuthStore()

    // Initialize the auth store on mount
    useEffect(() => {
        if (!isInitialized) {
            initialize()
        }
        setIsReady(true)

        // Check for email/password auth
        checkEmailPasswordAuth()
    }, [initialize, isInitialized])

    /**
     * Check if user is authenticated with email/password
     */
    const checkEmailPasswordAuth = async () => {
        try {
            // Check localStorage for user data
            const storedUser = localStorage.getItem('user')

            if (storedUser) {
                const user = JSON.parse(storedUser) as User

                setAuthState({
                    user,
                    isLoading: false,
                    isAuthenticated: true,
                    error: null,
                })
            } else {
                setAuthState({
                    user: null,
                    isLoading: false,
                    isAuthenticated: false,
                    error: null,
                })
            }
        } catch (error) {
            setAuthState({
                user: null,
                isLoading: false,
                isAuthenticated: false,
                error: 'Failed to check authentication status',
            })
        }
    }

    /**
     * Navigate to login page
     */
    const goToLogin = () => {
        router.push('/auth')
    }

    /**
     * Navigate to OTP verification page
     */
    const goToOtpVerification = () => {
        router.push('/auth/otp-verification')
    }

    /**
     * Navigate to home/dashboard page
     */
    const goToHome = () => {
        router.push('/')
    }

    /**
     * Require authentication or redirect to login
     * Also synchronizes authentication state between client and server
     *
     * @returns {boolean} True if authenticated, false otherwise
     */
    const requireAuth = () => {
        // Check both authentication methods
        const isUserAuthenticated = authState.isAuthenticated || (isReady && checkAuthentication())

        // Also check localStorage for isVerified flag (for backward compatibility)
        const isVerifiedInLocalStorage =
            typeof window !== 'undefined' && localStorage.getItem('isVerified') === 'true'

        // Check if we have isVerified in localStorage but not in cookies
        if (isVerifiedInLocalStorage) {
            // Set the cookie for server-side middleware if not already set
            if (typeof document !== 'undefined' && !document.cookie.includes('isVerified=true')) {
                document.cookie = 'isVerified=true; path=/; max-age=86400' // 24 hours
                console.log('[Auth] Synchronized auth state: Set isVerified cookie')
            }
        }

        // Determine final authentication status
        const finalAuthStatus = isUserAuthenticated || isVerifiedInLocalStorage

        console.log('[Auth] requireAuth check:', {
            isUserAuthenticated,
            isVerifiedInLocalStorage,
            finalAuthStatus,
            isLoading: storeIsLoading || authState.isLoading,
        })

        if (!finalAuthStatus && !storeIsLoading && !authState.isLoading) {
            console.log('[Auth] Not authenticated, redirecting to login')
            goToLogin()
            return false
        }

        return finalAuthStatus
    }

    /**
     * Handle login with OTP
     *
     * @param mobile - Mobile number
     * @param otp - OTP code
     * @param redirectUrl - URL to redirect to after successful login
     */
    const handleOtpLogin = async (mobile: string, otp: string, redirectUrl = '/') => {
        const success = await verifyOtp(mobile, otp)

        if (success) {
            router.push(redirectUrl)
        }

        return success
    }

    /**
     * Handle logout for both authentication methods
     * Calls the backend logout API to invalidate tokens
     *
     * @param redirectUrl - URL to redirect to after logout
     */
    const handleLogout = async (redirectUrl = '/auth') => {
        try {
            setAuthState((prev) => ({ ...prev, isLoading: true }))

            // Call the OTP auth logout which will call the backend API
            await storeLogout()

            // Clear email/password auth state from localStorage
            localStorage.removeItem('user')
            localStorage.removeItem('isVerified')
            localStorage.removeItem('isLoggedIn')
            localStorage.removeItem('phone')
            localStorage.removeItem('dialCode')
            localStorage.removeItem('authToken')

            // Clear authentication cookies
            document.cookie = 'isVerified=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
            document.cookie = 'session_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'

            console.log('[Auth] Logout: Cleared all auth data')

            // Update local state
            setAuthState({
                user: null,
                isLoading: false,
                isAuthenticated: false,
                error: null,
            })

            // Redirect to login page
            router.push(redirectUrl)
        } catch (error) {
            console.error('Logout error:', error)

            // Even if API call fails, clear local state
            localStorage.removeItem('user')
            localStorage.removeItem('isVerified')
            localStorage.removeItem('isLoggedIn')
            localStorage.removeItem('phone')
            localStorage.removeItem('dialCode')
            localStorage.removeItem('authToken')

            // Clear authentication cookies
            document.cookie = 'isVerified=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
            document.cookie = 'session_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'

            console.log('[Auth] Logout (error path): Cleared all auth data')

            setAuthState({
                user: null,
                isLoading: false,
                isAuthenticated: false,
                error: 'Logout partially failed, but you have been logged out locally',
            })

            // Still redirect to login page
            router.push(redirectUrl)
        }
    }

    /**
     * Login with email and password
     *
     * @param email - User email
     * @param password - User password
     * @returns Promise resolving to success status
     */
    const login = useCallback(async (email: string, password: string): Promise<boolean> => {
        try {
            setAuthState((prev) => ({ ...prev, isLoading: true, error: null }))

            // In a real app, this would be an API call to authenticate
            // Simulating API call with timeout
            await new Promise((resolve) => setTimeout(resolve, 1000))

            // Mock successful login for demo purposes
            if (email && password) {
                const user: User = {
                    id: '1',
                    name: 'John Doe',
                    email,
                    role: 'user',
                }

                // Store user in localStorage
                localStorage.setItem('user', JSON.stringify(user))

                setAuthState({
                    user,
                    isLoading: false,
                    isAuthenticated: true,
                    error: null,
                })

                return true
            }

            throw new Error('Invalid credentials')
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Login failed'

            setAuthState((prev) => ({
                ...prev,
                isLoading: false,
                isAuthenticated: false,
                error: errorMessage,
            }))

            return false
        }
    }, [])

    /**
     * Register a new user
     *
     * @param name - User name
     * @param email - User email
     * @param password - User password
     * @returns Promise resolving to success status
     */
    const register = useCallback(
        async (name: string, email: string, password: string): Promise<boolean> => {
            try {
                setAuthState((prev) => ({ ...prev, isLoading: true, error: null }))

                // In a real app, this would be an API call to register
                // Simulating API call with timeout
                await new Promise((resolve) => setTimeout(resolve, 1500))

                // Mock successful registration for demo purposes
                if (name && email && password) {
                    const user: User = {
                        id: '1',
                        name,
                        email,
                        role: 'user',
                    }

                    // Store user in localStorage
                    localStorage.setItem('user', JSON.stringify(user))

                    setAuthState({
                        user,
                        isLoading: false,
                        isAuthenticated: true,
                        error: null,
                    })

                    return true
                }

                throw new Error('Invalid registration data')
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Registration failed'

                setAuthState((prev) => ({
                    ...prev,
                    isLoading: false,
                    isAuthenticated: false,
                    error: errorMessage,
                }))

                return false
            }
        },
        [],
    )

    /**
     * Clear all error messages
     */
    const clearError = () => {
        storeClearError()
        setAuthState((prev) => ({ ...prev, error: null }))
    }

    // Check localStorage for isVerified flag (for backward compatibility)
    const isVerifiedInLocalStorage =
        typeof window !== 'undefined' && localStorage.getItem('isVerified') === 'true'

    // Determine if the user is authenticated by any method
    const isAuthenticated =
        authState.isAuthenticated || (isReady && checkAuthentication()) || isVerifiedInLocalStorage

    // Determine if any authentication operation is loading
    const isLoading = authState.isLoading || storeIsLoading

    // Combine error messages from both auth methods
    const error = authState.error || storeError

    /**
     * Refresh the access token if it's expired or about to expire
     *
     * @returns Promise resolving to success status
     */
    const refreshAuthToken = async (): Promise<boolean> => {
        // Check if we have tokens
        if (!tokens?.refreshToken) {
            return false
        }

        // Check if token is expired or about to expire (within 5 minutes)
        const isExpiredOrExpiringSoon =
            !tokens.expiresAt || tokens.expiresAt < Date.now() + 5 * 60 * 1000

        if (isExpiredOrExpiringSoon) {
            // Try to refresh the token
            return await refreshTokens(tokens.refreshToken)
        }

        // Token is still valid
        return true
    }

    return {
        // Combined state
        isAuthenticated,
        isLoading,
        isReady,
        error,
        tokens,

        // OTP auth state
        mobile,
        otpUser: storeUser,

        // Email/password auth state
        user: authState.user,

        // OTP auth actions
        requestOtp,
        verifyOtp,
        resendOtp,
        setMobile,

        // Email/password auth actions
        login,
        register,

        // Combined actions
        logout: handleLogout,
        clearError,

        // OTP-specific login
        otpLogin: handleOtpLogin,

        // Token management
        refreshAuthToken,
        updateTokens,

        // Navigation helpers
        goToLogin,
        goToOtpVerification,
        goToHome,
        requireAuth,
    }
}
