'use client'

import React, { useEffect, useState } from 'react'
import { BannerSection } from '../../types'
import { CheckCircle, ChevronLeft, ChevronRight } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { useDeviceContext } from '../../providers/DeviceProvider'

interface BannerProps {
    data: BannerSection
}

const Banner: React.FC<BannerProps> = ({ data }) => {
    const { label, value } = data
    const [currentBanner, setCurrentBanner] = useState(0)
    const banners = value.banners || []
    const isHeroBanner = banners.length > 0
    const isBlueBanner = value.features
    const { isMobile, isTablet, isDesktop, isMounted } = useDeviceContext()

    useEffect(() => {
        if (isHeroBanner) {
            const timer = setInterval(() => {
                setCurrentBanner((prev) => (prev + 1) % banners.length)
            }, 5000)

            return () => clearInterval(timer)
        }
    }, [banners.length, isHeroBanner])

    const nextBanner = () => {
        setCurrentBanner((prev) => (prev + 1) % banners.length)
    }

    const prevBanner = () => {
        setCurrentBanner((prev) => (prev - 1 + banners.length) % banners.length)
    }

    const MobileView = () => {
        if (!isHeroBanner || banners.length === 0) return null

        return (
            <section className="py-4 px-4 mt-4 w-full block sm:block md:hidden">
                <div className="w-full">
                    {/* Mobile banner card - horizontal layout with image on right */}
                    <div className="w-full rounded-xl overflow-hidden shadow-sm flex h-[140px]">
                        {/* Text content - left side */}
                        <div className="bg-blue-primary text-white p-4 w-2/3 flex flex-col justify-center">
                            <h2 className="text-[18px] font-bold leading-tight mb-4">
                                {banners[currentBanner].label}
                            </h2>
                            <p className="text-blue-100 text-xs">
                                {banners[currentBanner].subheading}
                            </p>
                        </div>

                        {/* Image - right side */}
                        <div className="w-1/3 h-full">
                            <img
                                src={banners[currentBanner].backgroundImage}
                                alt={banners[currentBanner].label}
                                className="w-full h-full object-cover"
                            />
                        </div>
                    </div>

                    {/* Pagination indicators */}
                    <div className="flex justify-center mt-3 w-full">
                        <div className="flex items-center gap-2">
                            {banners.map((_, index) => (
                                <div
                                    key={index}
                                    onClick={() => setCurrentBanner(index)}
                                    className={`h-[4px] w-8 cursor-pointer block ${
                                        index === currentBanner ? 'bg-blue-900' : 'bg-gray-300'
                                    }`}
                                    role="button"
                                    tabIndex={0}
                                    aria-label={`Go to slide ${index + 1}`}
                                />
                            ))}
                        </div>
                    </div>
                </div>
            </section>
        )
    }

    const TabletView = () => {
        if (!isHeroBanner || banners.length === 0) return null

        return (
            <section className="py-6 px-6 mt-6 w-full hidden md:block lg:hidden">
                <div className="w-full">
                    {/* Tablet banner card */}
                    <div className="w-full rounded-xl overflow-hidden shadow-md flex h-[160px]">
                        {/* Text content - left side */}
                        <div className="bg-blue-primary text-white p-5 w-3/5 flex flex-col justify-center">
                            <h2 className="text-[22px] font-bold leading-tight mb-4">
                                {banners[currentBanner].label}
                            </h2>
                            <p className="text-blue-100 text-sm">
                                {banners[currentBanner].subheading}
                            </p>
                        </div>

                        {/* Image - right side */}
                        <div className="w-2/5 h-full">
                            <img
                                src={banners[currentBanner].backgroundImage}
                                alt={banners[currentBanner].label}
                                className="w-full h-full object-cover"
                            />
                        </div>
                    </div>

                    {/* Pagination indicators */}
                    <div className="flex justify-center mt-4 w-full">
                        <div className="flex items-center gap-3">
                            {banners.map((_, index) => (
                                <div
                                    key={index}
                                    onClick={() => setCurrentBanner(index)}
                                    className={`h-[5px] w-10 cursor-pointer block ${
                                        index === currentBanner ? 'bg-blue-900' : 'bg-gray-300'
                                    }`}
                                    role="button"
                                    tabIndex={0}
                                    aria-label={`Go to slide ${index + 1}`}
                                />
                            ))}
                        </div>
                    </div>
                </div>
            </section>
        )
    }

    const DesktopView = () => {
        if (!isHeroBanner || banners.length === 0) return null

        return (
            <section className="py-8 px-10 w-full relative hidden lg:block">
                <div className="container">
                    <div className="relative">
                        {/* Navigation arrows */}
                        <button
                            onClick={prevBanner}
                            className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-6 bg-white rounded-full p-2 shadow-md z-10 hover:bg-gray-100"
                            aria-label="Previous banner"
                        >
                            <ChevronLeft size={24} className="text-gray-700" />
                        </button>

                        <div className="flex gap-4 overflow-hidden w-full">
                            {banners.map((banner, index) => (
                                <div
                                    key={index}
                                    className={`flex-shrink-0 w-[calc(50%-8px)] h-[140px] rounded-xl overflow-hidden shadow-sm flex ${
                                        index % 2 === 0 ? 'bg-blue-primary' : 'bg-green-700'
                                    } transition-transform duration-300 ease-in-out`}
                                    style={{
                                        transform: `translateX(calc(-${currentBanner}00% / ${banners.length > 1 ? 2 : 1}))`,
                                    }}
                                >
                                    {/* Text content - left side */}
                                    <div className="text-white p-6 w-1/2 flex flex-col justify-center">
                                        <h2 className="text-xl font-bold leading-tight mb-3">
                                            {banner.label}
                                        </h2>
                                        <p className="text-gray-200 text-sm">{banner.subheading}</p>
                                    </div>

                                    {/* Image - right side */}
                                    <div className="w-1/2 h-full">
                                        <img
                                            src={banner.backgroundImage}
                                            alt={banner.label}
                                            className="w-full h-full object-cover"
                                        />
                                    </div>
                                </div>
                            ))}
                        </div>

                        <button
                            onClick={nextBanner}
                            className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-6 bg-white rounded-full p-2 shadow-md z-10 hover:bg-gray-100"
                            aria-label="Next banner"
                        >
                            <ChevronRight size={24} className="text-gray-700" />
                        </button>
                    </div>
                </div>
            </section>
        )
    }

    if (!isMounted) return null
    if (isMobile) return <MobileView />
    if (isTablet) return <TabletView />
    return <DesktopView />
}

export default Banner
