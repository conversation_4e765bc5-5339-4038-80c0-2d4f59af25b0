import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import BrandsModal from './BrandsModal'
import { DeviceProvider } from '../../../providers/DeviceProvider'
import { DeviceType } from '../../../lib/constants/breakpoints'

// Mock createPortal to render in the same container
jest.mock('react-dom', () => ({
    ...jest.requireActual('react-dom'),
    createPortal: (node: React.ReactNode) => node,
}))

const mockBrands = [
    { id: '1', label: 'Brand 1', logoImage: 'https://example.com/brand1.png' },
    { id: '2', label: 'Brand 2', logoImage: 'https://example.com/brand2.png' },
    { id: '3', label: 'Brand 3', logoImage: 'https://example.com/brand3.png' },
]

const defaultProps = {
    isOpen: true,
    onClose: jest.fn(),
    brands: mockBrands,
    onBrandClick: jest.fn(),
}

const renderWithDeviceProvider = (ui: React.ReactElement, deviceType: DeviceType = DeviceType.Desktop) => {
    return render(
        <DeviceProvider initialDeviceType={deviceType}>
            {ui}
        </DeviceProvider>
    )
}

describe('BrandsModal', () => {
    beforeEach(() => {
        jest.clearAllMocks()
    })

    it('renders brands modal with correct title', () => {
        renderWithDeviceProvider(<BrandsModal {...defaultProps} />)
        
        expect(screen.getByText('Our Brands')).toBeInTheDocument()
        expect(screen.getByText('Showing 3 brands')).toBeInTheDocument()
    })

    it('renders all brands in the grid', () => {
        renderWithDeviceProvider(<BrandsModal {...defaultProps} />)
        
        expect(screen.getByLabelText('View services by Brand 1')).toBeInTheDocument()
        expect(screen.getByLabelText('View services by Brand 2')).toBeInTheDocument()
        expect(screen.getByLabelText('View services by Brand 3')).toBeInTheDocument()
    })

    it('calls onBrandClick when a brand is clicked', () => {
        const onBrandClick = jest.fn()
        renderWithDeviceProvider(<BrandsModal {...defaultProps} onBrandClick={onBrandClick} />)
        
        const brandButton = screen.getByLabelText('View services by Brand 1')
        fireEvent.click(brandButton)
        
        expect(onBrandClick).toHaveBeenCalledWith(mockBrands[0])
    })

    it('uses correct grid columns for mobile', () => {
        renderWithDeviceProvider(<BrandsModal {...defaultProps} />, DeviceType.Mobile)
        
        const grid = screen.getByRole('grid') || screen.getByText('Showing 3 brands').nextElementSibling
        expect(grid).toHaveClass('grid-cols-3')
    })

    it('uses correct grid columns for tablet', () => {
        renderWithDeviceProvider(<BrandsModal {...defaultProps} />, DeviceType.Tablet)
        
        const grid = screen.getByText('Showing 3 brands').nextElementSibling
        expect(grid).toHaveClass('grid-cols-4')
    })

    it('uses correct grid columns for desktop', () => {
        renderWithDeviceProvider(<BrandsModal {...defaultProps} />, DeviceType.Desktop)
        
        const grid = screen.getByText('Showing 3 brands').nextElementSibling
        expect(grid).toHaveClass('grid-cols-6')
    })

    it('shows empty state when no brands are provided', () => {
        renderWithDeviceProvider(<BrandsModal {...defaultProps} brands={[]} />)
        
        expect(screen.getByText('No brands available')).toBeInTheDocument()
        expect(screen.getByText('Check back later for brand updates.')).toBeInTheDocument()
    })

    it('shows correct brand count text for single brand', () => {
        const singleBrand = [mockBrands[0]]
        renderWithDeviceProvider(<BrandsModal {...defaultProps} brands={singleBrand} />)
        
        expect(screen.getByText('Showing 1 brand')).toBeInTheDocument()
    })

    it('renders brand images with correct alt text', () => {
        renderWithDeviceProvider(<BrandsModal {...defaultProps} />)
        
        const brandImage = screen.getByAltText('Brand 1 logo')
        expect(brandImage).toBeInTheDocument()
        expect(brandImage).toHaveAttribute('src', 'https://example.com/brand1.png')
    })

    it('does not render when isOpen is false', () => {
        renderWithDeviceProvider(<BrandsModal {...defaultProps} isOpen={false} />)
        
        expect(screen.queryByText('Our Brands')).not.toBeInTheDocument()
    })
})
