/**
 * Service Category Service for Frontend
 * Handles fetching service category data from the backend API
 * Follows the same pattern as other frontend services
 */

export interface ServiceCategory {
    id: string
    label: string
    description: string
    image: string
    iconName: string
}

export interface ServiceCategoryResponse {
    success: boolean
    data: ServiceCategory[]
    meta?: {
        total?: number
        page?: number
        limit?: number
        cached?: boolean
        cachedAt?: string
    }
    timestamp: string
}

export interface ServiceCategoryError {
    success: false
    error: string
    message: string
    timestamp: string
}

/**
 * Service Category API Service
 */
class ServiceCategoryApiService {
    private readonly baseUrl: string
    private readonly timeout: number

    constructor() {
        this.baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api'
        this.timeout = 10000 // 10 seconds
    }

    /**
     * Fetch service categories from backend API
     */
    async fetchServiceCategories(): Promise<ServiceCategory[]> {
        try {
            console.log('[ServiceCategoryService] Fetching service categories from API')

            const controller = new AbortController()
            const timeoutId = setTimeout(() => controller.abort(), this.timeout)

            const response = await fetch(`${this.baseUrl}/service-categories`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    Accept: 'application/json',
                },
                signal: controller.signal,
            })

            clearTimeout(timeoutId)

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`)
            }

            const result: ServiceCategoryResponse | ServiceCategoryError = await response.json()

            if (!result.success) {
                throw new Error(
                    (result as ServiceCategoryError).message ||
                        'Failed to fetch service categories',
                )
            }

            const successResult = result as ServiceCategoryResponse
            console.log(
                `[ServiceCategoryService] Successfully fetched ${successResult.data.length} service categories`,
            )

            return successResult.data
        } catch (error) {
            console.error('[ServiceCategoryService] Error fetching service categories:', error)

            if (error instanceof Error) {
                if (error.name === 'AbortError') {
                    throw new Error(
                        'Request timeout: Service categories API took too long to respond',
                    )
                }
                throw error
            }

            throw new Error('Unknown error occurred while fetching service categories')
        }
    }

    /**
     * Clear service categories cache on backend
     */
    async clearCache(): Promise<void> {
        try {
            console.log('[ServiceCategoryService] Clearing service categories cache')

            const controller = new AbortController()
            const timeoutId = setTimeout(() => controller.abort(), this.timeout)

            const response = await fetch(`${this.baseUrl}/service-categories/cache`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    Accept: 'application/json',
                },
                signal: controller.signal,
            })

            clearTimeout(timeoutId)

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`)
            }

            const result = await response.json()

            if (!result.success) {
                throw new Error(result.message || 'Failed to clear cache')
            }

            console.log('[ServiceCategoryService] Cache cleared successfully')
        } catch (error) {
            console.error('[ServiceCategoryService] Error clearing cache:', error)
            throw error
        }
    }

    /**
     * Get cache status from backend
     */
    async getCacheStatus(): Promise<{
        cacheEnabled: boolean
        cacheTTL: string
        lastUpdated: string
    }> {
        try {
            const controller = new AbortController()
            const timeoutId = setTimeout(() => controller.abort(), this.timeout)

            const response = await fetch(`${this.baseUrl}/service-categories/cache/status`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    Accept: 'application/json',
                },
                signal: controller.signal,
            })

            clearTimeout(timeoutId)

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`)
            }

            const result = await response.json()

            if (!result.success) {
                throw new Error(result.message || 'Failed to get cache status')
            }

            return result.data
        } catch (error) {
            console.error('[ServiceCategoryService] Error getting cache status:', error)
            throw error
        }
    }
}

// Create and export singleton instance
export const serviceCategoryService = new ServiceCategoryApiService()

// Export the class for testing purposes
export { ServiceCategoryApiService }
