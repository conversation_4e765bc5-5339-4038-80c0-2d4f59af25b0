/**
 * Database Health Service
 *
 * This service monitors database connectivity and provides health check functionality.
 * It implements circuit breaker pattern to prevent cascading failures.
 *
 * @module services/database-health
 */

import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
import { EventEmitter } from 'events';

// Circuit breaker states
enum CircuitState {
  CLOSED = 'CLOSED',     // Normal operation, requests pass through
  OPEN = 'OPEN',         // Circuit is open, requests fail fast
  HALF_OPEN = 'HALF_OPEN', // Testing if the service is back online
}

// Health check result
interface HealthCheckResult {
  status: 'UP' | 'DOWN' | 'DEGRADED';
  responseTime: number;
  timestamp: Date;
  details?: any;
}

/**
 * Database Health Service
 * Monitors database connectivity and implements circuit breaker pattern
 */
export class DatabaseHealthService extends EventEmitter {
  private prisma: PrismaClient;
  private state: CircuitState = CircuitState.CLOSED;
  private failureCount: number = 0;
  private lastFailureTime: number = 0;
  private readonly failureThreshold: number;
  private readonly resetTimeout: number;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private readonly checkIntervalMs: number;
  private lastHealthCheck: HealthCheckResult | null = null;

  /**
   * Creates a new DatabaseHealthService
   * 
   * @param {PrismaClient} prisma - The Prisma client instance
   * @param {Object} options - Configuration options
   * @param {number} options.failureThreshold - Number of failures before opening circuit (default: 3)
   * @param {number} options.resetTimeout - Time in ms before trying to close circuit again (default: 30000)
   * @param {number} options.checkInterval - Time in ms between health checks (default: 60000)
   */
  constructor(
    prisma: PrismaClient, 
    options: { 
      failureThreshold?: number, 
      resetTimeout?: number,
      checkInterval?: number
    } = {}
  ) {
    super();
    this.prisma = prisma;
    this.failureThreshold = options.failureThreshold || 3;
    this.resetTimeout = options.resetTimeout || 30000; // 30 seconds
    this.checkIntervalMs = options.checkInterval || 60000; // 1 minute
  }

  /**
   * Starts the health check monitoring
   */
  startMonitoring(): void {
    if (this.healthCheckInterval) {
      return; // Already monitoring
    }

    logger.info('Starting database health monitoring');
    
    // Perform initial health check
    this.checkHealth().catch(error => {
      logger.error('Initial health check failed:', error);
    });
    
    // Set up regular health checks
    this.healthCheckInterval = setInterval(() => {
      this.checkHealth().catch(error => {
        logger.error('Health check failed:', error);
      });
    }, this.checkIntervalMs);
  }

  /**
   * Stops the health check monitoring
   */
  stopMonitoring(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
      logger.info('Database health monitoring stopped');
    }
  }

  /**
   * Performs a health check on the database
   * 
   * @returns {Promise<HealthCheckResult>} The health check result
   */
  async checkHealth(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    let status: 'UP' | 'DOWN' | 'DEGRADED' = 'DOWN';
    let details: any = {};

    try {
      // Execute a simple query to check database connectivity
      await this.prisma.$queryRaw`SELECT 1 as health_check`;
      
      status = this.state === CircuitState.CLOSED ? 'UP' : 'DEGRADED';
      
      // Reset failure count on successful check
      this.failureCount = 0;
      
      // If circuit is half-open and check succeeds, close the circuit
      if (this.state === CircuitState.HALF_OPEN) {
        this.closeCircuit();
      }
    } catch (error: any) {
      status = 'DOWN';
      details.error = error.message;
      
      // Increment failure count
      this.failureCount++;
      this.lastFailureTime = Date.now();
      
      // If we've reached the failure threshold, open the circuit
      if (this.state === CircuitState.CLOSED && this.failureCount >= this.failureThreshold) {
        this.openCircuit();
      }
      
      logger.error('Database health check failed:', error);
    }

    const responseTime = Date.now() - startTime;
    
    const result: HealthCheckResult = {
      status,
      responseTime,
      timestamp: new Date(),
      details: {
        ...details,
        circuitState: this.state,
        failureCount: this.failureCount,
      },
    };
    
    this.lastHealthCheck = result;
    this.emit('healthCheck', result);
    
    return result;
  }

  /**
   * Gets the current health status
   * 
   * @returns {HealthCheckResult | null} The last health check result or null if no check has been performed
   */
  getStatus(): HealthCheckResult | null {
    return this.lastHealthCheck;
  }

  /**
   * Opens the circuit
   * 
   * @private
   */
  private openCircuit(): void {
    logger.warn(`Opening circuit breaker after ${this.failureCount} consecutive failures`);
    this.state = CircuitState.OPEN;
    this.emit('circuitOpen', { failureCount: this.failureCount });
    
    // Schedule half-open state after reset timeout
    setTimeout(() => {
      if (this.state === CircuitState.OPEN) {
        logger.info('Moving circuit breaker to half-open state');
        this.state = CircuitState.HALF_OPEN;
        this.emit('circuitHalfOpen');
      }
    }, this.resetTimeout);
  }

  /**
   * Closes the circuit
   * 
   * @private
   */
  private closeCircuit(): void {
    logger.info('Closing circuit breaker');
    this.state = CircuitState.CLOSED;
    this.failureCount = 0;
    this.emit('circuitClosed');
  }

  /**
   * Checks if the circuit is closed (normal operation)
   * 
   * @returns {boolean} True if the circuit is closed
   */
  isCircuitClosed(): boolean {
    return this.state === CircuitState.CLOSED;
  }
}

// Factory function to create a database health service
export const createDatabaseHealthService = (
  prisma: PrismaClient,
  options?: { failureThreshold?: number, resetTimeout?: number, checkInterval?: number }
): DatabaseHealthService => {
  return new DatabaseHealthService(prisma, options);
};
