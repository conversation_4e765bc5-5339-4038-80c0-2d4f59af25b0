/**
 * Error Handler Utility
 *
 * This module provides standardized error handling functions for the application.
 * It ensures consistent error responses across all endpoints and middlewares.
 *
 * @module utils/errorHandler
 */

import { Request, Response, NextFunction } from 'express';
import { StatusCodes, getReasonPhrase } from 'http-status-codes';
import { buildAPIResponse } from './apiResponse';

/**
 * Error codes for the application
 */
export enum ErrorCode {
    // Authentication errors
    AUTHENTICATION_REQUIRED = 'AUTHENTICATION_REQUIRED',
    INVALID_TOKEN = 'INVALID_TOKEN',
    TOKEN_EXPIRED = 'TOKEN_EXPIRED',
    TOKEN_GENERATION_FAILED = 'TOKEN_GENERATION_FAILED',

    // CSRF errors
    CSRF_TOKEN_MISSING = 'CSRF_TOKEN_MISSING',
    CSRF_TOKEN_INVALID = 'CSRF_TOKEN_INVALID',

    // Validation errors
    INVALID_REQUEST = 'INVALID_REQUEST',
    INVALID_MOBILE_FORMAT = 'INVALID_MOBILE_FORMAT',
    INVALID_OTP_FORMAT = 'INVALID_OTP_FORMAT',

    // OTP errors
    OTP_REQUEST_FAILED = 'OTP_REQUEST_FAILED',
    OTP_VERIFICATION_FAILED = 'OTP_VERIFICATION_FAILED',
    INVALID_OTP = 'INVALID_OTP',

    // Rate limiting errors
    RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
    AUTH_RATE_LIMIT_EXCEEDED = 'AUTH_RATE_LIMIT_EXCEEDED',
    OTP_REQUEST_RATE_LIMIT_EXCEEDED = 'OTP_REQUEST_RATE_LIMIT_EXCEEDED',
    OTP_VERIFICATION_RATE_LIMIT_EXCEEDED = 'OTP_VERIFICATION_RATE_LIMIT_EXCEEDED',

    // SMS errors
    SMS_SEND_FAILED = 'SMS_SEND_FAILED',

    // Server errors
    INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
    SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',

    // Generic errors
    UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

/**
 * Interface for error details
 */
export interface ErrorDetails {
    code: ErrorCode | string;
    message?: string;
    details?: any;
}

/**
 * Creates a standardized error response
 *
 * @param statusCode - HTTP status code
 * @param message - Error message
 * @param errorCode - Error code
 * @param details - Additional error details
 * @returns Standardized API response
 */
export const createErrorResponse = (
    statusCode: number,
    message: string,
    errorCode: ErrorCode | string,
    details?: any
) => {
    return buildAPIResponse(statusCode, message, null, {
        code: errorCode,
        message,
        details: process.env.NODE_ENV === 'production' ? undefined : details,
    });
};

/**
 * Global error handler middleware
 */
export const globalErrorHandler = (
    err: any,
    _req: Request,
    res: Response,
    _next: NextFunction
) => {
    console.error('Unhandled error:', err);

    // Don't expose error details in production
    const message =
        process.env.NODE_ENV === 'production'
            ? 'Internal Server Error'
            : err.message || 'Something went wrong';

    const errorResponse = createErrorResponse(
        StatusCodes.INTERNAL_SERVER_ERROR,
        message,
        ErrorCode.INTERNAL_SERVER_ERROR,
        process.env.NODE_ENV === 'production' ? undefined : err.stack
    );

    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json(errorResponse);
};

/**
 * Validation error handler
 */
export const handleValidationError = (
    res: Response,
    message: string,
    errorCode: ErrorCode = ErrorCode.INVALID_REQUEST
) => {
    const errorResponse = createErrorResponse(
        StatusCodes.BAD_REQUEST,
        message,
        errorCode
    );

    res.status(StatusCodes.BAD_REQUEST).json(errorResponse);
};

/**
 * Authentication error handler
 */
export const handleAuthError = (
    res: Response,
    message: string,
    errorCode: ErrorCode = ErrorCode.AUTHENTICATION_REQUIRED
) => {
    const errorResponse = createErrorResponse(
        StatusCodes.UNAUTHORIZED,
        message,
        errorCode
    );

    res.status(StatusCodes.UNAUTHORIZED).json(errorResponse);
};

/**
 * Rate limit error handler
 */
export const handleRateLimitError = (
    res: Response,
    message: string,
    errorCode: ErrorCode = ErrorCode.RATE_LIMIT_EXCEEDED
) => {
    const errorResponse = createErrorResponse(
        StatusCodes.TOO_MANY_REQUESTS,
        message,
        errorCode
    );

    res.status(StatusCodes.TOO_MANY_REQUESTS).json(errorResponse);
};
