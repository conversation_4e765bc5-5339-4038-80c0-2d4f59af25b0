'use client'

import React, { memo } from 'react'
import LocationLoader from '../LocationLoader'
import LocationDisplay from '../LocationDisplay'
import LocationError from '../LocationError'
import useLocationFetcher from '@/hooks/useLocationFetcher'

/**
 * Main component that handles fetching and displaying location
 * Using React.memo to prevent unnecessary re-renders
 */
const LocationFetcher: React.FC = memo(() => {
    const { loading, locationDetails, error, refetch } = useLocationFetcher()

    if (error) {
        return <LocationError message={error} onRetry={refetch} />
    }

    return loading ? (
        <LocationLoader />
    ) : (
        <LocationDisplay
            address={locationDetails?.formattedAddress || 'Address not available'}
            postalCode={locationDetails?.postalCode}
        />
    )
})

// Add display name for better debugging
LocationFetcher.displayName = 'LocationFetcher'

export default LocationFetcher
