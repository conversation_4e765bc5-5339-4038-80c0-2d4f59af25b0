/**
 * Static text hook to replace i18n functionality
 * This provides a simplified API-compatible replacement for useI18n
 */

import { Language } from '../types'

// Define the return type to match useI18n
interface StaticTextHook {
    /**
     * Current language (always 'en')
     */
    language: Language

    /**
     * No-op function to maintain API compatibility
     */
    setLanguage: (lang: Language) => void

    /**
     * Returns hardcoded text values
     */
    t: (key: string, params?: Record<string, string>) => string

    /**
     * Available languages (only English)
     */
    availableLanguages: Language[]

    /**
     * Always false
     */
    isLoading: boolean
}

/**
 * Hook that provides static text values
 * This is a replacement for useI18n that doesn't require a provider
 *
 * @returns Static text utilities
 */
export function useStaticText(): StaticTextHook {
    // Function to get static text
    const t = (key: string, params?: Record<string, string>): string => {
        // Common UI text
        if (key === 'common.shop') return 'Shop'
        if (key === 'common.cart') return 'Cart'
        if (key === 'common.account') return 'Account'
        if (key === 'common.notifications') return 'Notifications'
        if (key === 'home.for_business') return 'For Business'

        // Error messages
        if (key === 'errors.try_again') return 'Try again'
        if (key === 'errors.unknown_section') return 'Unknown Section Type'
        if (key === 'errors.section_error_title') return 'Error loading section'
        if (key === 'errors.server_error') return 'Server error'

        // Parameterized messages
        if (key === 'errors.section_error_message' && params?.section) {
            return `We're sorry, but there was an error loading the ${params.section} section.`
        }
        if (key === 'errors.no_component_found' && params?.type) {
            return `No component found for section type: ${params.type}`
        }

        // Removed accessibility text

        // For all other keys, just return the last part of the key as a readable string
        const parts = key.split('.')
        const lastPart = parts[parts.length - 1]

        // Convert camelCase or snake_case to Title Case with spaces
        return lastPart
            .replace(/_/g, ' ')
            .replace(/([A-Z])/g, ' $1')
            .replace(/^\w/, (c) => c.toUpperCase())
    }

    return {
        language: 'en',
        setLanguage: () => {}, // No-op function
        t,
        availableLanguages: ['en'],
        isLoading: false,
    }
}

// For TypeScript compatibility with existing code
export type { Language }
