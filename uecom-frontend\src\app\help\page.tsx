'use client'

import { useState, useEffect } from 'react'
import { ChevronLeft, CheckCircle, AlertCircle } from 'lucide-react'
import { FormField } from '@/components/ui/atoms/form-field'
import { Button } from '@/components/ui/atoms/Button'
import AppLayout from '@/components/layout/AppLayout'
import AccountMenu from '@/components/navigation/accoutMenu'
import { useRouter } from 'next/navigation'
import { layoutService } from '@/services/LayoutService'
import { SectionData } from '@/types'

// Form validation types
type FormErrors = {
    name?: string
    phone?: string
    email?: string
    message?: string
}

export default function HelpPage() {
    const [formData, setFormData] = useState({
        name: '',
        phone: '',
        email: '',
        orderId: '',
        message: '',
    })
    const [errors, setErrors] = useState<FormErrors>({})
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')
    const [footerSection, setFooterSection] = useState<SectionData | undefined>(undefined)

    const router = useRouter()

    // Fetch footer section data from API
    useEffect(() => {
        const fetchFooterData = async () => {
            try {
                // Use the layoutService which has been updated to use the API data source
                console.log('Fetching footer data using layoutService')

                // Fetch page data from the API
                const pageData = await layoutService.getPageData('home')

                // Find the footer section
                const footer = pageData.sections.find((section) => section.type === 'footer')

                if (footer) {
                    setFooterSection(footer)
                } else {
                    console.warn('No footer section found in API data')
                }
            } catch (error) {
                console.error('Error fetching footer data from API:', error)
            }
        }

        fetchFooterData()
    }, [])

    // Validate form data
    const validateForm = (): boolean => {
        const newErrors: FormErrors = {}

        // Name validation
        if (!formData.name.trim()) {
            newErrors.name = 'Name is required'
        }

        // Phone validation
        if (!formData.phone.trim()) {
            newErrors.phone = 'Phone number is required'
        } else if (!/^[0-9]{10}$/.test(formData.phone.trim())) {
            newErrors.phone = 'Please enter a valid 10-digit phone number'
        }

        // Email validation (only if provided)
        if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
            newErrors.email = 'Please enter a valid email address'
        }

        // Message validation
        if (!formData.message.trim()) {
            newErrors.message = 'Message is required'
        }

        setErrors(newErrors)
        return Object.keys(newErrors).length === 0
    }

    const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
        event.preventDefault()

        // Validate form
        if (!validateForm()) {
            return
        }

        setIsSubmitting(true)

        try {
            // Simulate API call
            await new Promise((resolve) => setTimeout(resolve, 1500))

            // Success handling
            setSubmitStatus('success')

            // Reset form after 3 seconds on success
            setTimeout(() => {
                setFormData({
                    name: '',
                    phone: '',
                    email: '',
                    orderId: '',
                    message: '',
                })
                setSubmitStatus('idle')
            }, 3000)
        } catch (error) {
            console.error('Error submitting form:', error)
            setSubmitStatus('error')

            // Reset error state after 3 seconds
            setTimeout(() => {
                setSubmitStatus('idle')
            }, 3000)
        } finally {
            setIsSubmitting(false)
        }
    }

    const handleChange = (field: string, value: string) => {
        setFormData((prev) => ({ ...prev, [field]: value }))

        // Clear error for this field when user starts typing
        if (errors[field as keyof FormErrors]) {
            setErrors((prev) => ({ ...prev, [field]: undefined }))
        }
    }

    return (
        <AppLayout sections={footerSection ? [footerSection] : []}>
            <main className="flex flex-col items-center w-full sm:pb-[100px] md:pb-[120px] lg:pb-[150px] xl:pb-[180px]">
                <div className="w-full max-w-[1000px] mx-auto px-4 sm:px-6 md:px-8 py-4 sm:py-6 md:py-10 bg-white">
                    <div className="flex flex-col lg:flex-row w-full gap-4 md:gap-6">
                        <AccountMenu />

                        <div className="flex-1 lg:mt-10">
                            {/* Mobile Header */}
                            <div className="flex items-center gap-2 mb-4 lg:hidden">
                                <button onClick={() => router.back()} aria-label="Go back">
                                    <ChevronLeft className="h-5 w-5 text-blue" />
                                </button>
                                <span className="text-lg font-poppins font-bold text-blue-primary">
                                    Help & Support
                                </span>
                            </div>

                            <div className="w-full bg-white shadow-md rounded-md p-4 sm:p-6">
                                {/* Status Messages */}
                                {submitStatus === 'success' && (
                                    <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md flex items-center gap-2 text-green-700">
                                        <CheckCircle className="h-5 w-5" />
                                        <span>
                                            Your message has been sent successfully! We'll get back
                                            to you soon.
                                        </span>
                                    </div>
                                )}

                                {submitStatus === 'error' && (
                                    <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-center gap-2 text-red-700">
                                        <AlertCircle className="h-5 w-5" />
                                        <span>
                                            There was an error sending your message. Please try
                                            again later.
                                        </span>
                                    </div>
                                )}

                                <h2 className="text-lg sm:text-xl font-bold text-blue-primary mb-4">
                                    Write your concern to us
                                </h2>

                                <form onSubmit={handleSubmit} className="space-y-4">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <FormField
                                                label="Your Name"
                                                type="text"
                                                name="name"
                                                required
                                                value={formData.name}
                                                onChange={(value) => handleChange('name', value)}
                                            />
                                            {errors.name && (
                                                <p className="mt-1 text-sm text-red-600">
                                                    {errors.name}
                                                </p>
                                            )}
                                        </div>

                                        <div>
                                            <FormField
                                                label="Phone Number"
                                                type="tel"
                                                name="phone"
                                                required
                                                placeholder="10-digit mobile number"
                                                value={formData.phone}
                                                onChange={(value) => handleChange('phone', value)}
                                            />
                                            {errors.phone && (
                                                <p className="mt-1 text-sm text-red-600">
                                                    {errors.phone}
                                                </p>
                                            )}
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <FormField
                                                label="Email Address"
                                                type="email"
                                                name="email"
                                                value={formData.email}
                                                onChange={(value) => handleChange('email', value)}
                                            />
                                            {errors.email && (
                                                <p className="mt-1 text-sm text-red-600">
                                                    {errors.email}
                                                </p>
                                            )}
                                        </div>

                                        <FormField
                                            label="Order ID (If applicable)"
                                            type="text"
                                            name="orderId"
                                            value={formData.orderId}
                                            onChange={(value) => handleChange('orderId', value)}
                                        />
                                    </div>

                                    <div>
                                        <FormField
                                            label="Your Message"
                                            type="textarea"
                                            name="message"
                                            required
                                            value={formData.message}
                                            onChange={(value) => handleChange('message', value)}
                                        />
                                        {errors.message && (
                                            <p className="mt-1 text-sm text-red-600">
                                                {errors.message}
                                            </p>
                                        )}
                                    </div>

                                    <Button
                                        type="submit"
                                        className={`w-full bg-orange-primary hover:bg-orange/90 text-white font-medium py-3 rounded-md flex justify-center items-center ${isSubmitting ? 'opacity-70 cursor-not-allowed' : ''}`}
                                        disabled={isSubmitting}
                                    >
                                        {isSubmitting ? (
                                            <>
                                                <span className="mr-2">Submitting...</span>
                                                <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                            </>
                                        ) : (
                                            'Submit'
                                        )}
                                    </Button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </AppLayout>
    )
}
