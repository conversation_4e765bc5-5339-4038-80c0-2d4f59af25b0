import type { Metadata } from 'next'
import './globals.css'
import { Poppins } from 'next/font/google'
import { ThemeProvider } from '../providers/ThemeProvider'
import ClientDeviceProvider from '../providers/ClientDeviceProvider'
import { AuthProvider } from '../providers/AuthProvider'
import SessionTimeoutHandler from '@/components/auth/SessionTimeoutHandler'

const poppins = Poppins({
    subsets: ['latin'],
    weight: ['300', '400', '500', '600', '700'], // Add required weights
    variable: '--font-poppins',
    display: 'swap', // Ensure text remains visible during font loading
})

export const metadata: Metadata = {
    title: 'Wify - We Install For You',
    description:
        'Book trained technicians for home services. Professional installation and repair services for all your needs.',
    keywords: 'home services, technicians, installation, repair, professional services',
    authors: [{ name: 'Wify Team' }],
    robots: 'index, follow',
    openGraph: {
        title: 'Wify - We Install For You',
        description: 'Book trained technicians for home services',
        url: 'https://wify.com',
        siteName: 'Wify',
        locale: 'en_US',
        type: 'website',
    },
    // Add accessibility metadata
    other: {
        'apple-mobile-web-app-capable': 'yes',
        'apple-mobile-web-app-status-bar-style': 'default',
        'format-detection': 'telephone=no',
        'mobile-web-app-capable': 'yes',
        'msapplication-tap-highlight': 'no',
    },
}

// Separate viewport export as per Next.js 14+ requirements
export const viewport = {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 5,
    minimumScale: 1,
    userScalable: true, // Allow users to zoom for accessibility
    themeColor: '#202e4b',
}

export default function RootLayout({ children }: { children: React.ReactNode }) {
    return (
        <html lang="en" className={poppins.variable}>
            <head>
                {/* Preconnect to critical domains */}
                <link rel="preconnect" href="https://fonts.googleapis.com" />
                <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

                {/* Add accessibility meta tags */}
                <meta name="theme-color" content="#202e4b" />
            </head>
            <body>
                <ThemeProvider>
                    <AuthProvider>
                        <SessionTimeoutHandler>
                            <ClientDeviceProvider>
                                {/* Main content wrapper with ID for skip link */}
                                <main id="main-content">{children}</main>
                            </ClientDeviceProvider>
                        </SessionTimeoutHandler>
                    </AuthProvider>
                </ThemeProvider>
            </body>
        </html>
    )
}
