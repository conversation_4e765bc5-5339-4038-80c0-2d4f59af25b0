import { authService } from '../../src/services/auth';

describe('AuthService', () => {
    beforeEach(() => {
        (authService as any).otpStore.clear();
    });

    describe('healthCheck', () => {
        it('should return OK status', () => {
            const result = authService.healthCheck();
            expect(result).toBe('OK');
        });
    });

    describe('requestOtp', () => {
        it('should generate and store OTP for valid mobile number', () => {
            const mobile = '**********';
            const result = authService.requestOtp(mobile);

            expect(result).toHaveProperty('message', 'OTP sent successfully');
            expect(result).toHaveProperty('mobile', mobile);
            expect(result).toHaveProperty('otp');
            expect(result.otp).toMatch(/^\d{6}$/);

            // Verify OTP is stored
            const storedOtp = (authService as any).otpStore.get(mobile);
            expect(storedOtp).toBe(result.otp);
        });

        it('should generate different OTPs for consecutive requests', () => {
            const mobile = '**********';
            const result1 = authService.requestOtp(mobile);
            const result2 = authService.requestOtp(mobile);

            expect(result1.otp).not.toBe(result2.otp);
        });
    });

    describe('verifyOtp', () => {
        it('should verify correct OTP successfully', () => {
            const mobile = '**********';
            const { otp } = authService.requestOtp(mobile);

            const result = authService.verifyOtp(mobile, otp);
            expect(result).toEqual({
                message: `Mobile number ${mobile} verified successfully`,
            });
        });

        it('should return error for incorrect OTP', () => {
            const mobile = '**********';
            authService.requestOtp(mobile);

            const result = authService.verifyOtp(mobile, '999999');
            expect(result).toEqual({
                error: 'Invalid OTP or mobile number',
            });
        });

        it('should return error for non-existent mobile number', () => {
            const result = authService.verifyOtp('**********', '123456');
            expect(result).toEqual({
                error: 'Invalid OTP or mobile number',
            });
        });

        it('should delete OTP after successful verification', () => {
            const mobile = '**********';
            const { otp } = authService.requestOtp(mobile);

            authService.verifyOtp(mobile, otp);

            const secondAttempt = authService.verifyOtp(mobile, otp);
            expect(secondAttempt).toEqual({
                error: 'Invalid OTP or mobile number',
            });
        });

        it('should handle multiple users with different OTPs', () => {
            const mobile1 = '**********';
            const mobile2 = '9876543211';

            const { otp: otp1 } = authService.requestOtp(mobile1);
            const { otp: otp2 } = authService.requestOtp(mobile2);

            const result1 = authService.verifyOtp(mobile1, otp1);
            const result2 = authService.verifyOtp(mobile2, otp2);

            expect(result1).toEqual({
                message: `Mobile number ${mobile1} verified successfully`,
            });
            expect(result2).toEqual({
                message: `Mobile number ${mobile2} verified successfully`,
            });
        });
    });
});
