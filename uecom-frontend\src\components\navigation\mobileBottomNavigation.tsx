import ClientMobileNavigation from './ClientMobileNavigation'

/**
 * MobileBottomNavigation component (Server Component)
 * Prepares navigation data and passes it to the client component
 *
 * Note: This component now uses default tabs with icon names as strings
 * The ClientMobileNavigation component will handle the icon mapping
 */
export default function MobileBottomNavigation() {
    // Use default tabs with icon names as strings for SSR compatibility
    const tabs = [
        { id: 'home', icon: 'Home', label: 'Home', path: '/discovery' },
        { id: 'shop', icon: 'ShoppingBag', label: 'Shop', path: '/shop' },
        { id: 'track', icon: 'Map', label: 'Track', path: '/track' },
        { id: 'profile', icon: 'User', label: 'Account', path: '/profile' },
    ]

    // Pass the prepared tabs to the client component
    return <ClientMobileNavigation tabs={tabs} />
}
