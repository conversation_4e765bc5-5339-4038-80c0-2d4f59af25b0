/**
 * Dependency Injection Container
 *
 * This module provides a simple dependency injection container for the application.
 * It follows the Service Locator pattern to register and resolve dependencies.
 *
 * @module di/container
 */

type Constructor<T> = new (...args: any[]) => T;
type Factory<T> = () => T;

/**
 * Simple dependency injection container
 */
export class Container {
    private static instance: Container;
    private services: Map<string, any> = new Map();
    private factories: Map<string, Factory<any>> = new Map();
    private singletons: Map<string, boolean> = new Map();

    /**
     * Get the singleton instance of the container
     */
    public static getInstance(): Container {
        if (!Container.instance) {
            Container.instance = new Container();
        }
        return Container.instance;
    }

    /**
     * Register a service with the container
     *
     * @param token - The token to register the service under
     * @param service - The service constructor or instance
     * @param isSingleton - Whether the service should be a singleton
     */
    public register<T>(
        token: string,
        service: Constructor<T> | T,
        isSingleton = true
    ): void {
        this.services.set(token, service);
        this.singletons.set(token, isSingleton);
    }

    /**
     * Register a factory function with the container
     *
     * @param token - The token to register the factory under
     * @param factory - The factory function
     * @param isSingleton - Whether the factory should produce a singleton
     */
    public registerFactory<T>(
        token: string,
        factory: Factory<T>,
        isSingleton = true
    ): void {
        this.factories.set(token, factory);
        this.singletons.set(token, isSingleton);
    }

    /**
     * Resolve a service from the container
     *
     * @param token - The token to resolve
     * @returns The resolved service
     */
    public resolve<T>(token: string): T {
        // Check if we have a factory for this token
        if (this.factories.has(token)) {
            const factory = this.factories.get(token)!;
            const isSingleton = this.singletons.get(token) || false;

            // If it's a singleton and we've already created it, return the instance
            if (isSingleton && this.services.has(`instance:${token}`)) {
                return this.services.get(`instance:${token}`);
            }

            // Create a new instance
            const instance = factory();

            // If it's a singleton, store the instance
            if (isSingleton) {
                this.services.set(`instance:${token}`, instance);
            }

            return instance;
        }

        // Check if we have a service for this token
        if (this.services.has(token)) {
            const service = this.services.get(token);
            const isSingleton = this.singletons.get(token) || false;

            // If the service is already an instance, return it
            if (typeof service !== 'function') {
                return service;
            }

            // If it's a singleton and we've already created it, return the instance
            if (isSingleton && this.services.has(`instance:${token}`)) {
                return this.services.get(`instance:${token}`);
            }

            // Create a new instance
            const instance = new (service as Constructor<T>)();

            // If it's a singleton, store the instance
            if (isSingleton) {
                this.services.set(`instance:${token}`, instance);
            }

            return instance;
        }

        throw new Error(`Service not found: ${token}`);
    }
}

// Export a singleton instance
export const container = Container.getInstance();
