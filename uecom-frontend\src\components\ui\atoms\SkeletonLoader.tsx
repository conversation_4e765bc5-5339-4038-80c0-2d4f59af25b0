'use client'

import React, { useMemo } from 'react'
import clsx from 'clsx'

interface SkeletonLoaderProps {
    width?: string
    height?: string
    className?: string
    rounded?: string
    variant?: 'rect' | 'circle' | 'text' | 'image' | 'icon'
    theme?: 'light' | 'dark'
    /**
     * Accessibility label for screen readers
     */
    ariaLabel?: string
}

const themeClasses = {
    light: 'bg-gray-200 animate-pulse',
    dark: 'bg-gray-700 animate-pulse',
}

/**
 * SkeletonLoader component for displaying loading states
 * Optimized with useMemo for performance and enhanced with accessibility
 *
 * @example
 * ```tsx
 * <SkeletonLoader variant="text" width="w-full" />
 * <SkeletonLoader variant="circle" ariaLabel="Loading profile image" />
 * ```
 */
const SkeletonLoader: React.FC<SkeletonLoaderProps> = React.memo(
    ({
        width,
        height,
        className = '',
        rounded,
        variant = 'rect',
        theme = 'light',
        ariaLabel = 'Loading content',
    }) => {
        // Memoize variant styles to prevent recalculation on re-renders
        const variantStyles = useMemo(() => {
            switch (variant) {
                case 'circle':
                    return 'w-[48px] h-[48px] rounded-full'
                case 'text':
                    return 'w-full h-4 rounded'
                case 'image':
                    return 'w-full h-[200px] rounded-md'
                case 'icon':
                    return 'w-[32px] h-[32px] rounded-md'
                default:
                    return ''
            }
        }, [variant])

        return (
            <div
                className={clsx(
                    'skeleton-loader overflow-hidden',
                    variantStyles,
                    themeClasses[theme],
                    width,
                    height,
                    rounded,
                    className,
                )}
                role="status"
                aria-label={ariaLabel}
                aria-busy="true"
                aria-live="polite"
            />
        )
    },
)

// Add display name for better debugging
SkeletonLoader.displayName = 'SkeletonLoader'

export default SkeletonLoader
