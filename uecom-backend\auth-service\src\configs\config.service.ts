/**
 * Configuration Service
 *
 * This service provides access to environment variables and configuration settings.
 * It follows the Singleton pattern to ensure only one instance exists.
 *
 * @module configs/config
 */

import { IConfigService } from '../interfaces/config-service.interface';
import { validateConfigOrExit } from './config.validator';

export class ConfigService implements IConfigService {
    constructor() {
        // Validate configuration on instantiation
        validateConfigOrExit();
    }
    /**
     * Gets the port number for the server
     *
     * @returns {number} The port number
     */
    getPort(): number {
        return Number(process.env.PORT);
    }

    /**
     * Gets the database URL
     *
     * @returns {string} The database URL
     */
    getDatabaseUrl(): string {
        // This is optional for auth service, so we'll return an empty string if not set
        return process.env.DATABASE_URL || '';
    }

    /**
     * Gets the JWT secret
     *
     * @returns {string} The JWT secret
     */
    getJwtSecret(): string {
        // This is validated at startup, so we can safely access it
        return process.env.JWT_SECRET || '';
    }

    /**
     * Gets the JWT session token expiration time in hours
     *
     * @returns {number} The JWT session token expiration time in hours
     */
    getJwtSessionExpiryHours(): number {
        return Number(process.env.JWT_SESSION_EXPIRY_HOURS || '24');
    }

    /**
     * Gets the current Node environment
     *
     * @returns {string} The Node environment
     */
    getNodeEnv(): string {
        return process.env.NODE_ENV || 'development';
    }

    /**
     * Gets the MTalkz API key
     *
     * @returns {string} The MTalkz API key
     */
    getMtalkzApiKey(): string {
        // This is validated at startup, so we can safely access it
        return process.env.MTALKZ_API_KEY || '';
    }

    /**
     * Gets the MTalkz sender ID
     *
     * @returns {string} The MTalkz sender ID
     */
    getMtalkzSenderId(): string {
        return process.env.MTALKZ_SENDER_ID || 'INWIFY';
    }

    /**
     * Gets the message base URL
     *
     * @returns {string} The message base URL
     */
    getMessageBaseUrl(): string {
        return process.env.MSG_BASE_URL || 'https://api.mtalkz.com';
    }

    /**
     * Gets the message sub-path
     *
     * @returns {string} The message sub-path
     */
    getMessageSubPath(): string {
        return process.env.MSG_SUB_PATH || '/V2/http-api.php';
    }

    /**
     * Gets the allowed origins for CORS
     *
     * @returns {string[]} The allowed origins
     */
    getAllowedOrigins(): string[] {
        return (process.env.ALLOWED_ORIGINS || 'http://localhost:3000').split(
            ','
        );
    }

    /**
     * Gets the Redis URL from environment variables or returns a default
     *
     * @returns {string} The Redis URL
     */
    getRedisUrl(): string {
        return process.env.REDIS_URL || 'redis://localhost:6379';
    }

    /**
     * Gets the Redis prefix for OTP keys
     *
     * @returns {string} The Redis prefix for OTP keys
     */
    getRedisOtpPrefix(): string {
        return process.env.REDIS_OTP_PREFIX || 'otp:';
    }

    /**
     * Gets the default OTP expiration time in seconds
     *
     * @returns {number} The default OTP expiration time in seconds
     */
    getOtpExpirationSeconds(): number {
        return Number(process.env.OTP_EXPIRATION_SECONDS || 900); // 15 minutes
    }
}

// Factory function for creating a config service
export const createConfigService = (): IConfigService => {
    return new ConfigService();
};

// Export a singleton instance for backward compatibility
export const configService = new ConfigService();
