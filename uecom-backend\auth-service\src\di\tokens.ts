/**
 * Service Tokens
 *
 * This module defines the tokens used for dependency injection.
 * These tokens are used to register and resolve services from the container.
 *
 * @module di/tokens
 */

export const SERVICE_TOKENS = {
    // Config
    CONFIG_SERVICE: 'config.service',

    // Storage
    OTP_STORAGE: 'otp.storage',
    REDIS_SERVICE: 'redis.service',
    REDIS_REPOSITORY: 'redis.repository',

    // SMS
    SMS_SERVICE: 'sms.service',

    // Auth
    AUTH_SERVICE: 'auth.service',
    AUTH_STRATEGY: 'auth.strategy',
    TOKEN_SERVICE: 'token.service',
    COOKIE_SERVICE: 'cookie.service',

    // Database
    DATABASE_SERVICE: 'database.service',
    TRANSACTION_SERVICE: 'transaction.service',
    DATABASE_HEALTH_SERVICE: 'database.health.service',
    DISTRIBUTED_TRANSACTION_SERVICE: 'distributed.transaction.service',

    // HTTP
    HTTP_SERVICE: 'http.service',

    // Factories
    AUTH_STRATEGY_FACTORY: 'auth.strategy.factory',
};
