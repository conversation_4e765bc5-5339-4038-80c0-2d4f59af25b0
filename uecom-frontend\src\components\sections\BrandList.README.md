# Our Brands Section

A responsive "Our Brands" section for the Discovery Page that displays brand logos in a grid layout with a modal popup for viewing all brands.

## Features

### 🎯 **Core Functionality**

- **Responsive Design**: Different layouts for mobile, tablet, and desktop
- **Modal Integration**: "View All" button opens a centered modal with all brands
- **Smooth Animations**: CSS transitions and modal animations
- **Accessibility Compliant**: Keyboard navigation, focus trapping, ARIA labels
- **Performance Optimized**: Lazy loading for modal images

### 📱 **Responsive Layouts**

#### Desktop View

- **Grid**: Horizontal row of 9 brand logos + "View All" button as 10th item
- **Layout**: `grid-cols-10` with 4px gaps
- **Button**: "View All" button integrated as last grid item
- **Modal**: Centered modal with 6-column grid

#### Tablet View

- **Grid**: 2 rows of 5 items each (9 brands + "View All" button)
- **Layout**: `grid-cols-5` with 3px gaps
- **Button**: "View All" button integrated as last grid item
- **Modal**: Large modal with 4-column grid

#### Mobile View

- **Grid**: 2 rows of 5 items each (9 brands + "View All" button)
- **Layout**: `grid-cols-5` with 2px gaps
- **Button**: "View All" button integrated as last grid item
- **Modal**: Full-screen modal with 3-column grid

## Components

### 1. **BrandList.tsx** (Main Component)

```tsx
interface BrandListProps {
    data: BrandListSection
}
```

**Responsibilities:**

- Renders responsive brand grids
- Manages modal state
- Handles brand click events
- Provides "View All" functionality

### 2. **Modal.tsx** (Base Modal)

```tsx
interface ModalProps {
    isOpen: boolean
    onClose: () => void
    title: string
    children: React.ReactNode
    size?: 'sm' | 'default' | 'lg' | 'xl' | 'full'
    // ... other props
}
```

**Features:**

- Focus trapping
- Escape key to close
- Click outside to close
- Smooth animations
- Accessibility compliant
- Portal rendering

### 3. **BrandsModal.tsx** (Brands-Specific Modal)

```tsx
interface BrandsModalProps {
    isOpen: boolean
    onClose: () => void
    brands: Brand[]
    onBrandClick?: (brand: Brand) => void
}
```

**Features:**

- Responsive grid layouts
- Brand count display
- Empty state handling
- Hover effects
- Brand name overlays

## Usage

### Basic Implementation

```tsx
import BrandList from '@/components/sections/BrandList'

const brands = [
    { id: '1', label: 'Brand 1', logoImage: 'url1' },
    { id: '2', label: 'Brand 2', logoImage: 'url2' },
    // ... more brands
]

<BrandList
    data={{
        type: 'brand-list',
        label: 'Our Brands',
        value: { brands }
    }}
/>
```

### Modal Usage (Standalone)

```tsx
import BrandsModal from '@/components/ui/molecules/BrandsModal'

;<BrandsModal
    isOpen={isModalOpen}
    onClose={() => setIsModalOpen(false)}
    brands={brands}
    onBrandClick={(brand) => console.log('Brand clicked:', brand)}
/>
```

## Accessibility Features

### ✅ **Keyboard Navigation**

- Tab through all interactive elements
- Enter/Space to activate buttons
- Escape to close modal
- Focus trapping within modal

### ✅ **Screen Reader Support**

- Proper ARIA labels and roles
- Modal title for screen readers
- Brand descriptions
- Loading states announced

### ✅ **Focus Management**

- Focus returns to trigger button on close
- First focusable element focused on open
- Visual focus indicators

## Styling & Animations

### **CSS Classes Used**

```css
/* Grid Layouts */
.grid-cols-5    /* Mobile/Tablet: 5 columns */
.grid-cols-10   /* Desktop: 10 columns */
.grid-cols-3    /* Modal Mobile: 3 columns */
.grid-cols-4    /* Modal Tablet: 4 columns */
.grid-cols-6    /* Modal Desktop: 6 columns */

/* Animations */
.animate-fade-in
.animate-slide-in-from-bottom
.animate-zoom-in
.transition-all
.duration-200
.duration-300

/* Hover Effects */
.hover:shadow-md
.hover:shadow-xl
.hover:scale-105
.group-hover:opacity-100
```

### **Custom Animations** (Tailwind Config)

```js
animation: {
    'fade-in': 'fadeIn 0.3s ease-out',
    'slide-in-from-bottom': 'slideInFromBottom 0.3s ease-out',
    'zoom-in': 'zoomIn 0.3s ease-out',
}
```

## Testing

### **Test Coverage**

- ✅ Modal open/close functionality
- ✅ Keyboard navigation
- ✅ Responsive layouts
- ✅ Brand click handling
- ✅ Accessibility compliance
- ✅ Empty states
- ✅ Animation behavior

### **Running Tests**

```bash
npm test BrandList
npm test Modal
npm test BrandsModal
```

## Performance Considerations

### **Optimizations**

- Lazy loading for modal images
- Memoized components where appropriate
- Efficient re-renders with proper keys
- Portal rendering for modals
- CSS-only animations

### **Bundle Size**

- Minimal dependencies
- Tree-shakeable components
- Optimized imports

## Browser Support

- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+
- ✅ Mobile browsers

## Future Enhancements

### **Potential Improvements**

- [ ] Brand search/filter functionality
- [ ] Brand categories/grouping
- [ ] Infinite scroll for large brand lists
- [ ] Brand detail pages integration
- [ ] Analytics tracking for brand clicks
- [ ] A/B testing for layout variations
