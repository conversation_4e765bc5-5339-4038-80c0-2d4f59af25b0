/**
 * Page Data Controller
 *
 * This controller handles requests for page data.
 */

import { Request, Response, RequestHandler } from 'express';
import { pageDataService } from '../services/page-data';
import { logger } from '../utils/logger';

/**
 * Export the controller functions
 */
export const pageDataController = {
    /**
     * Get page data by page name
     */
    getPageData: (async (req: Request, res: Response) => {
        try {
            const { pageName } = req.params;

            // Validate page name
            if (!pageName) {
                res.status(400).json({
                    error: 'Missing required parameter: pageName',
                    message: 'Page name is required',
                });
                return;
            }

            logger.info(`Received request for page data: ${pageName}`);

            // Get page data from service
            const data = await pageDataService.getPageData(pageName);

            // Return page data
            res.status(200).json(data);
        } catch (error) {
            logger.error('Error in getPageData controller:', error);
            res.status(500).json({
                error: 'Internal server error',
                message: 'Failed to fetch page data',
            });
        }
    }) as RequestHand<PERSON>,

    /**
     * Get all available page data
     */
    getAllPageData: (async (_req: Request, res: Response) => {
        try {
            logger.info('Received request for all page data');

            // Get page data for 'home' page (currently we only have one page)
            const data = await pageDataService.getPageData('home');

            // Return page data
            res.status(200).json(data);
        } catch (error) {
            logger.error('Error in getAllPageData controller:', error);
            res.status(500).json({
                error: 'Internal server error',
                message: 'Failed to fetch page data',
            });
        }
    }) as RequestHandler,
};
