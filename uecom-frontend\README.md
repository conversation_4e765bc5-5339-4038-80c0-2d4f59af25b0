This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

The project uses a `.env` file in the root directory with the following environment variables:

```bash
# API URLs
NEXT_PUBLIC_APP_SERVICE_URL=http://localhost:8000
NEXT_PUBLIC_AUTH_SERVICE_URL=http://localhost:4000
NEXT_PUBLIC_ECOM_AUTH_SERVICE_URL=http://localhost:4000/v1/auth
NEXT_PUBLIC_BASE_URL=http://localhost:3000
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000/api

# For server-side API calls (not exposed to the client)
APP_SERVICE_URL=http://localhost:8000
AUTH_SERVICE_URL=http://localhost:4000

# Server-side only API key (not exposed to client)
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
```

Make sure to update the API keys and URLs as needed for your environment.

Then, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Page Data Architecture

The application is designed to fetch page data from the backend API. The data flow works as follows:

1. The frontend attempts to fetch page data from the backend API at `/api/page-data/:pageName`
2. If the API is not available or returns an error, the frontend falls back to using minimal mock data for development and testing purposes
3. The mock data is structured the same way as the API response, but with minimal content to make it clear that it's mock data

### API Data Source

The application uses a fallback mechanism to ensure it always has data to display:

1. First, it tries to fetch data from the backend API
2. If the API is unavailable or returns an error, it falls back to minimal mock data
3. The mock data is clearly labeled as such to avoid confusion

### Using Mock Data for Testing

If you need to use mock data for testing purposes, you can use the `createMockLayoutService` function:

```typescript
import { createMockLayoutService } from '@/services/LayoutService'

// Create a mock layout service
const mockService = createMockLayoutService()

// Get page data from the mock service
const pageData = await mockService.getPageData('home')
```

Note that the mock data is minimal and only intended for testing and development when the backend API is unavailable.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
