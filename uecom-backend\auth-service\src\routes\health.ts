/**
 * Health Check Routes
 *
 * This module defines routes for checking the health of the service.
 *
 * @module routes/health
 */

import express from 'express';
import {
    basicHealthCheck,
    detailedHealthCheck,
} from '../controllers/health.controller';

const router = express.Router();

/**
 * @route GET /health
 * @description Basic health check endpoint
 * @access Public
 */
router.get('/', basicHealthCheck);

/**
 * @route GET /health/detailed
 * @description Detailed health check endpoint with dependency status
 * @access Public
 */
router.get('/detailed', detailedHealthCheck);

export default router;
