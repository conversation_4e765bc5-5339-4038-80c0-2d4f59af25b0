# Wify UECOM Project

A modern e-commerce platform built with Next.js, TypeScript, and Tailwind CSS.

## Project Structure

The project is organized into multiple services:

- **uecom-frontend**: Next.js frontend application
- **uecom-backend-node**: Node.js backend services
  - **app-service**: Main application service
  - **auth-service**: Authentication service

## Frontend Architecture

The frontend follows a modular architecture with:

- Next.js App Router for routing
- TypeScript for type safety
- Tailwind CSS for styling
- Atomic design pattern for UI components
- Service layer for data fetching

See the [Frontend README](./uecom-frontend/src/README.md) for more details on the frontend architecture.

## Getting Started

### Prerequisites

- Node.js 18+
- Yarn or npm

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/your-username/wify-uecom.git
   cd wify-uecom
   ```

2. Install dependencies:
   ```bash
   # Install frontend dependencies
   cd uecom-frontend
   yarn install
   
   # Install backend dependencies
   cd ../uecom-backend-node/app-service
   yarn install
   
   cd ../auth-service
   yarn install
   ```

3. Start the development servers:
   ```bash
   # Start frontend
   cd uecom-frontend
   yarn dev
   
   # Start backend services (in separate terminals)
   cd uecom-backend-node/app-service
   yarn dev
   
   cd uecom-backend-node/auth-service
   yarn dev
   ```

## Development Workflow

1. Create a new branch for your feature or bug fix
2. Make your changes
3. Run tests to ensure everything works
4. Submit a pull request

## Testing

```bash
# Run frontend tests
cd uecom-frontend
yarn test

# Run backend tests
cd uecom-backend-node/app-service
yarn test

cd uecom-backend-node/auth-service
yarn test
```

## Deployment

The application is deployed using Vercel for the frontend and serverless functions for the backend.

## Contributing

Please read [CONTRIBUTING.md](./CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## License

This project is licensed under the MIT License - see the [LICENSE](./LICENSE) file for details.
