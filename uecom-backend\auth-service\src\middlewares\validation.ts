import { Request, Response, NextFunction, RequestHandler } from 'express';
import { handleValidationError, ErrorCode } from '../utils/errorHandler';

/**
 * Validates OTP request parameters
 *
 * @param req - Express request object
 * @param res - Express response object
 * @param next - Express next function
 */
export const validateRequestOtp: RequestHandler = (req, res, next) => {
    const { mobile } = req.body;

    if (!mobile || typeof mobile !== 'string' || !/^\d{10}$/.test(mobile)) {
        handleValidationError(
            res,
            'Invalid mobile number format',
            ErrorCode.INVALID_MOBILE_FORMAT
        );
        return;
    }

    next();
};

/**
 * Validates OTP verification parameters
 *
 * @param req - Express request object
 * @param res - Express response object
 * @param next - Express next function
 */
export const validateVerifyOtp: RequestHandler = (req, res, next) => {
    const { mobile, otp } = req.body;

    // Validate mobile number
    if (!mobile || typeof mobile !== 'string' || !/^\d{10}$/.test(mobile)) {
        handleValidationError(
            res,
            'Invalid mobile number format',
            ErrorCode.INVALID_MOBILE_FORMAT
        );
        return;
    }

    // Validate OTP
    if (!otp || typeof otp !== 'string' || !/^\d{6}$/.test(otp)) {
        handleValidationError(
            res,
            'Invalid OTP format',
            ErrorCode.INVALID_OTP_FORMAT
        );
        return;
    }

    next();
};
