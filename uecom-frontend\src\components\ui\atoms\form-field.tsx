'use client'

import { Input } from '@/components/ui/atoms/input'
import { Textarea } from '@/components/ui/atoms/textarea'
import { Label } from '@/components/ui/atoms/label'

interface FormFieldProps {
    label: string
    type: 'text' | 'email' | 'tel' | 'textarea'
    name: string
    required?: boolean
    placeholder?: string
    value: string
    onChange: (value: string) => void
}

export function FormField({
    label,
    type,
    name,
    required = false,
    placeholder,
    value,
    onChange,
}: FormFieldProps) {
    return (
        <div className="space-y-2">
            <Label htmlFor={name} className="text-[15px] text-blue font-poppins">
                {label} {required && <span className="text-orange">*</span>}
            </Label>
            {type === 'textarea' ? (
                <Textarea
                    id={name}
                    name={name}
                    required={required}
                    placeholder={placeholder}
                    value={value}
                    onChange={(e) => onChange(e.target.value)}
                    className="min-h-[120px] resize-none bg-white border-gray-400  focus:ring-1 focus:ring-orange"
                />
            ) : (
                <Input
                    type={type}
                    id={name}
                    name={name}
                    required={required}
                    placeholder={placeholder}
                    value={value}
                    onChange={(e) => onChange(e.target.value)}
                    className="bg-white  bg-white border-gray-400  focus:ring-1 focus:ring-orange"
                />
            )}
        </div>
    )
}
