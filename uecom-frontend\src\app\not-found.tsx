import React from 'react'
import Link from 'next/link'
import { Home, Search, ArrowLeft } from 'lucide-react'

/**
 * 404 Not Found page component
 * This page is shown when a route doesn't exist
 */
export default function NotFound() {
    return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
            <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
                {/* 404 Illustration */}
                <div className="mb-8">
                    <div className="text-6xl font-bold text-gray-300 mb-4">404</div>
                    <div className="w-24 h-24 mx-auto bg-blue-100 rounded-full flex items-center justify-center">
                        <Search className="h-12 w-12 text-blue-600" />
                    </div>
                </div>

                {/* Error Title */}
                <h1 className="text-2xl font-bold text-gray-900 mb-4">
                    Page Not Found
                </h1>

                {/* Error Message */}
                <p className="text-gray-600 mb-8">
                    Sorry, we couldn't find the page you're looking for. 
                    The page might have been moved, deleted, or you entered the wrong URL.
                </p>

                {/* Action Buttons */}
                <div className="space-y-3">
                    <Link
                        href="/"
                        className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
                    >
                        <Home className="h-4 w-4" />
                        Go to Home
                    </Link>
                    
                    <button
                        onClick={() => window.history.back()}
                        className="w-full bg-gray-100 hover:bg-gray-200 text-gray-900 font-semibold py-3 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
                    >
                        <ArrowLeft className="h-4 w-4" />
                        Go Back
                    </button>
                </div>

                {/* Popular Links */}
                <div className="mt-8 pt-6 border-t border-gray-200">
                    <h3 className="text-sm font-semibold text-gray-900 mb-3">
                        Popular Pages
                    </h3>
                    <div className="space-y-2">
                        <Link
                            href="/discovery"
                            className="block text-blue-600 hover:text-blue-700 text-sm underline"
                        >
                            Discover Services
                        </Link>
                        <Link
                            href="/auth"
                            className="block text-blue-600 hover:text-blue-700 text-sm underline"
                        >
                            Sign In
                        </Link>
                        <Link
                            href="/help"
                            className="block text-blue-600 hover:text-blue-700 text-sm underline"
                        >
                            Help & Support
                        </Link>
                    </div>
                </div>
            </div>
        </div>
    )
}
