'use client'

import React, { useState } from 'react'
import { ServiceCategoriesSection } from '../../types'
import { useDeviceContext } from '../../providers/DeviceProvider'
import { useServiceCategories } from '../../hooks/useServiceCategories'
import { ServiceCategorySectionSkeleton } from '../ui/molecules/ServiceCategorySkeleton'
import { AlertTriangle, RefreshCw } from 'lucide-react'

interface ServiceCategoriesProps {
    data: ServiceCategoriesSection
}

const ServiceCategories: React.FC<ServiceCategoriesProps> = ({ data }) => {
    const { label, value } = data
    const { categories: staticCategories } = value
    const [selectedCategory, setSelectedCategory] = useState<any>(null)
    const { isMobile, isTablet, isMounted } = useDeviceContext()

    // Use dynamic service categories hook
    const { serviceCategories, loading, error, refetch } = useServiceCategories()

    // Use dynamic data if available, fallback to static data
    const categories = serviceCategories.length > 0 ? serviceCategories : staticCategories

    // Error State Component
    const ErrorState = ({ error, onRetry }: { error: string; onRetry: () => void }) => (
        <div className="px-4 py-8 text-center">
            <div className="max-w-md mx-auto">
                <div className="flex justify-center mb-4">
                    <div className="bg-red-100 rounded-full p-3">
                        <AlertTriangle className="h-8 w-8 text-red-600" />
                    </div>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Failed to load service categories
                </h3>
                <p className="text-gray-600 mb-4 text-sm">{error}</p>
                <button
                    onClick={onRetry}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium flex items-center gap-2 mx-auto transition-colors"
                >
                    <RefreshCw className="h-4 w-4" />
                    Try Again
                </button>
            </div>
        </div>
    )

    // Show loading skeleton while fetching data
    if (loading && serviceCategories.length === 0) {
        return <ServiceCategorySectionSkeleton />
    }

    // Show error state with retry option
    if (
        error &&
        serviceCategories.length === 0 &&
        (!staticCategories || staticCategories.length === 0)
    ) {
        return <ErrorState error={error} onRetry={refetch} />
    }

    if (!categories || categories.length === 0) {
        return null
    }

    const handleCategoryClick = (category: any) => {
        setSelectedCategory(category)
    }

    if (selectedCategory) {
        // This would be the detailed view for a selected category
        return (
            <div className="px-4 mt-10">
                <button
                    onClick={() => setSelectedCategory(null)}
                    className="flex items-center gap-2 text-[#1E293B] mb-4"
                >
                    <span className="font-medium">← Back to {label}</span>
                </button>
                <h2 className="text-xl font-bold text-[#1E293B] mb-4">{selectedCategory.label}</h2>
                <p>{selectedCategory.description}</p>
            </div>
        )
    }

    const MobileView = () => (
        <div className="px-4 mt-8 block sm:block md:hidden">
            <h2 className="text-md font-bold text-[#1E293B] mb-4">{label}</h2>
            <div className="grid grid-cols-3 gap-3">
                {categories.map((category) => (
                    <div
                        key={category.label}
                        className="bg-gray-100 p-4 rounded-xl shadow-sm border hover:shadow-md transition-shadow relative h-[120px] overflow-hidden cursor-pointer"
                        onClick={() => handleCategoryClick(category)}
                    >
                        <div className="absolute -top-2 -right-2">
                            <div className="bg-gray-300 w-16 h-16 rounded-full flex items-center justify-center p-2">
                                <div className="relative top-3 right-3">
                                    <img
                                        className="w-14 h-14 object-contain"
                                        src={category.image}
                                        alt={`${category.label} service category icon`}
                                        loading="lazy"
                                        onError={(e) => {
                                            const target = e.target as HTMLImageElement
                                            target.src = '/images/fallback-service-icon.svg'
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                        <div className="absolute bottom-1 left-2 ">
                            <h3 className="text-[#1E293B] text-sm font-medium mt-2">
                                {category.label}
                            </h3>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    )

    const TabletView = () => (
        <div className="px-6 mt-6 hidden md:block lg:hidden">
            <h2 className="text-lg font-bold text-[#1E293B] mb-5">{label}</h2>
            <div className="grid grid-cols-4 gap-4">
                {categories.map((category) => (
                    <div
                        key={category.label}
                        className="bg-gray-100 p-4 rounded-xl shadow-sm hover:shadow-md relative h-[120px] overflow-hidden cursor-pointer"
                        onClick={() => handleCategoryClick(category)}
                    >
                        <div className="absolute -top-2 -right-2">
                            <div className="bg-gray-300 w-20 h-20 rounded-full flex items-center justify-center p-2">
                                <div className="relative top-4 right-4">
                                    <img
                                        className="w-16 h-16 object-contain"
                                        src={category.image}
                                        alt={`${category.label} service category icon`}
                                        loading="lazy"
                                        onError={(e) => {
                                            const target = e.target as HTMLImageElement
                                            target.src = '/images/fallback-service-icon.svg'
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                        <div className="absolute bottom-2 left-2">
                            <h3 className="text-[#1E293B] text-sm font-medium">{category.label}</h3>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    )

    const DesktopView = () => (
        <div className="px-10 mt-2 hidden lg:block">
            <h2 className="text-l font-bold text-[#1E293B] mb-6">{label}</h2>
            <div className="grid grid-cols-6 gap-4">
                {categories.map((category) => (
                    <div
                        key={category.label}
                        className="bg-gray-100 p-4 rounded-xl shadow-sm hover:shadow-md relative h-[120px] overflow-hidden cursor-pointer"
                        onClick={() => handleCategoryClick(category)}
                    >
                        <div className="absolute -top-2 -right-2">
                            <div className="bg-gray-300 w-22 h-22 rounded-full flex items-center justify-center p-2">
                                <div className="relative top-5 right-5">
                                    <img
                                        className="w-20 h-20 object-contain"
                                        src={category.image}
                                        alt={`${category.label} service category icon`}
                                        loading="lazy"
                                        onError={(e) => {
                                            const target = e.target as HTMLImageElement
                                            target.src = '/images/fallback-service-icon.svg'
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                        <div className="absolute bottom-2 left-3">
                            <h3 className="text-[#1E293B] text-sm font-medium">{category.label}</h3>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    )

    if (!isMounted) return null
    if (selectedCategory) {
        // This would be the detailed view for a selected category
        return (
            <div className="px-4 mt-10">
                <button
                    onClick={() => setSelectedCategory(null)}
                    className="flex items-center gap-2 text-[#1E293B] mb-4"
                >
                    <span className="font-medium">← Back to {label}</span>
                </button>
                <h2 className="text-xl font-bold text-[#1E293B] mb-4">{selectedCategory.label}</h2>
                <p>{selectedCategory.description}</p>
            </div>
        )
    }

    if (isMobile) return <MobileView />
    if (isTablet) return <TabletView />
    return <DesktopView />
}
export default ServiceCategories
