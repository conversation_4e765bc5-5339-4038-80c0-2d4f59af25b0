'use client'

import { useEffect, useState, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuth'

// Default timeout values
const DEFAULT_TIMEOUT_WARNING = 5 * 60 * 1000 // 5 minutes before expiry
const DEFAULT_CHECK_INTERVAL = 60 * 1000 // Check every minute

interface SessionTimeoutHandlerProps {
    children: React.ReactNode
    timeoutWarning?: number // Time in ms before expiry to show warning
    checkInterval?: number // Time in ms between checks
}

/**
 * Session Timeout Handler Component
 *
 * This component:
 * 1. Monitors token expiration
 * 2. Shows a warning when the session is about to expire
 * 3. Attempts to refresh the token automatically
 * 4. Logs the user out if the token can't be refreshed
 *
 * @param props Component props
 * @returns JSX element
 */
export default function SessionTimeoutHandler({
    children,
    timeoutWarning = DEFAULT_TIMEOUT_WARNING,
    checkInterval = DEFAULT_CHECK_INTERVAL,
}: SessionTimeoutHandlerProps) {
    const { tokens, isAuthenticated, refreshAuthToken, logout } = useAuth()
    const [showTimeoutWarning, setShowTimeoutWarning] = useState(false)
    const [timeRemaining, setTimeRemaining] = useState<number | null>(null)

    /**
     * Check if the session is about to expire
     */
    const checkSessionTimeout = useCallback(async () => {
        // Only check if user is authenticated and has tokens
        if (!isAuthenticated || !tokens?.expiresAt) {
            setShowTimeoutWarning(false)
            return
        }

        // Calculate time until expiry
        const timeUntilExpiry = tokens.expiresAt - Date.now()

        // If token is expired, try to refresh it
        if (timeUntilExpiry <= 0) {
            const refreshed = await refreshAuthToken()
            if (!refreshed) {
                // If refresh failed, log the user out
                await logout()
            }
            return
        }

        // If token is about to expire, show warning
        if (timeUntilExpiry <= timeoutWarning) {
            setShowTimeoutWarning(true)
            setTimeRemaining(Math.floor(timeUntilExpiry / 1000))
        } else {
            setShowTimeoutWarning(false)
        }
    }, [isAuthenticated, tokens, timeoutWarning, refreshAuthToken, logout])

    /**
     * Handle extending the session
     */
    const handleExtendSession = async () => {
        const refreshed = await refreshAuthToken()
        if (refreshed) {
            setShowTimeoutWarning(false)
        } else {
            // If refresh failed, show error or log out
            await logout()
        }
    }

    // Set up interval to check session timeout
    useEffect(() => {
        // Initial check
        checkSessionTimeout()

        // Set up interval for subsequent checks
        const intervalId = setInterval(checkSessionTimeout, checkInterval)

        // Clean up interval on unmount
        return () => clearInterval(intervalId)
    }, [checkSessionTimeout, checkInterval])

    // Update time remaining every second when warning is shown
    useEffect(() => {
        if (!showTimeoutWarning || !tokens?.expiresAt) return

        const updateTimeRemaining = () => {
            if (!tokens.expiresAt) return // TypeScript guard

            const remaining = Math.max(0, tokens.expiresAt - Date.now())
            setTimeRemaining(Math.floor(remaining / 1000))

            // If time is up, try to refresh
            if (remaining <= 0) {
                handleExtendSession()
            }
        }

        const timerId = setInterval(updateTimeRemaining, 1000)
        return () => clearInterval(timerId)
    }, [showTimeoutWarning, tokens?.expiresAt, handleExtendSession])

    return (
        <>
            {children}

            {/* Session timeout warning dialog */}
            {showTimeoutWarning && timeRemaining !== null && (
                <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
                    <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
                        <h3 className="text-xl font-semibold mb-4">Session Timeout Warning</h3>
                        <p className="mb-4">
                            Your session will expire in {Math.floor(timeRemaining / 60)}:
                            {(timeRemaining % 60).toString().padStart(2, '0')}.
                        </p>
                        <p className="mb-6">Would you like to extend your session?</p>
                        <div className="flex justify-end space-x-4">
                            <button
                                onClick={() => logout()}
                                className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100"
                            >
                                Logout
                            </button>
                            <button
                                onClick={handleExtendSession}
                                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                            >
                                Extend Session
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </>
    )
}
