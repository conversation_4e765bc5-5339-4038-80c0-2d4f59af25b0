/**
 * Enhanced Authentication Store
 *
 * This module implements the Observer pattern for managing authentication state.
 * It uses Zustand for state management with middleware for persistence and devtools integration.
 * It provides a clean API for authentication operations with improved error handling and state persistence.
 *
 * @module stores/auth/auth-store-enhanced
 */

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { requestOTP, verifyOTP, resendOTP, logoutUser } from '@/app/api/auth'
import { devtools } from 'zustand/middleware'

/**
 * User profile interface
 */
interface UserProfile {
    id: string
    mobile: string
    name?: string
    email?: string
}

/**
 * Authentication tokens interface
 */
interface AuthTokens {
    accessToken: string
    refreshToken?: string
    expiresIn: number
    expiresAt?: number
}

/**
 * Enhanced authentication state interface
 */
interface AuthState {
    // State
    isAuthenticated: boolean
    isLoading: boolean
    isInitialized: boolean
    mobile: string
    user: UserProfile | null
    tokens: AuthTokens | null
    error: string | null
    lastUpdated: number

    // Actions
    setMobile: (mobile: string) => void
    requestOtp: (mobile: string) => Promise<boolean>
    verifyOtp: (mobile: string, otp: string) => Promise<boolean>
    resendOtp: () => Promise<boolean>
    logout: () => Promise<void>
    clearError: () => void
    initialize: () => void
    checkAuthentication: () => boolean
    refreshTokens: (refreshToken: string) => Promise<boolean>
    updateTokens: (accessToken: string, refreshToken?: string, expiresIn?: number) => void
}

/**
 * Enhanced authentication store using Zustand with persistence and devtools
 * Implements the Observer pattern for state management
 */
export const useAuthStore = create<AuthState>()(
    devtools(
        persist(
            (set, get) => ({
                // Initial state
                isAuthenticated: false,
                isLoading: false,
                isInitialized: false,
                mobile: '',
                user: null,
                tokens: null,
                error: null,
                lastUpdated: Date.now(),

                /**
                 * Initialize the auth store
                 * Checks token expiration and sets authentication state
                 */
                initialize: () => {
                    const { tokens } = get()

                    // Check if we have tokens and if they're still valid
                    if (tokens?.accessToken && tokens?.expiresAt) {
                        const isValid = tokens.expiresAt > Date.now()
                        set({
                            isAuthenticated: isValid,
                            isInitialized: true,
                        })

                        // If token is expired, clear it
                        if (!isValid) {
                            set({ tokens: null, user: null })
                        }
                    } else {
                        set({ isInitialized: true })
                    }
                },

                /**
                 * Check if the user is authenticated with a valid token
                 *
                 * @returns {boolean} True if authenticated with valid token
                 */
                checkAuthentication: () => {
                    const { tokens } = get()

                    if (!tokens?.accessToken || !tokens?.expiresAt) {
                        return false
                    }

                    return tokens.expiresAt > Date.now()
                },

                // Actions
                setMobile: (mobile: string) => set({ mobile }),

                /**
                 * Request an OTP for the provided mobile number
                 *
                 * @param {string} mobile - The mobile number
                 * @returns {Promise<boolean>} True if the request was successful
                 */
                requestOtp: async (mobile: string) => {
                    try {
                        set({ isLoading: true, error: null, mobile })

                        const response = await requestOTP(mobile)

                        // Check if the response indicates success (status code 200-299)
                        const isSuccess = response.statusCode >= 200 && response.statusCode < 300

                        if (!isSuccess) {
                            set({
                                isLoading: false,
                                error:
                                    response.error?.message ||
                                    response.message ||
                                    'Failed to send OTP',
                                lastUpdated: Date.now(),
                            })
                            return false
                        }

                        set({ isLoading: false, lastUpdated: Date.now() })
                        return true
                    } catch (error) {
                        set({
                            isLoading: false,
                            error:
                                error instanceof Error
                                    ? error.message
                                    : 'An unknown error occurred',
                            lastUpdated: Date.now(),
                        })
                        return false
                    }
                },

                /**
                 * Verify an OTP for the current mobile number
                 *
                 * @param {string} mobile - The mobile number
                 * @param {string} otp - The OTP to verify
                 * @returns {Promise<boolean>} True if the verification was successful
                 */
                verifyOtp: async (mobile: string, otp: string) => {
                    try {
                        set({ isLoading: true, error: null })

                        const response = await verifyOTP(mobile, otp)

                        // Check if the response indicates success (status code 200-299)
                        const isSuccess = response.statusCode >= 200 && response.statusCode < 300

                        if (!isSuccess) {
                            set({
                                isLoading: false,
                                error: response.error?.message || response.message || 'Invalid OTP',
                                lastUpdated: Date.now(),
                            })
                            return false
                        }

                        // Store user and token information
                        if (response.data) {
                            const { tokens, user } = response.data

                            // Calculate token expiration time
                            const expiresAt = tokens?.expiresIn
                                ? Date.now() + tokens.expiresIn * 1000
                                : undefined

                            set({
                                isLoading: false,
                                isAuthenticated: true,
                                user,
                                tokens: tokens ? { ...tokens, expiresAt } : null,
                                lastUpdated: Date.now(),
                            })
                        } else {
                            set({
                                isLoading: false,
                                isAuthenticated: true,
                                lastUpdated: Date.now(),
                            })
                        }

                        return true
                    } catch (error) {
                        set({
                            isLoading: false,
                            error:
                                error instanceof Error
                                    ? error.message
                                    : 'An unknown error occurred',
                            lastUpdated: Date.now(),
                        })
                        return false
                    }
                },

                /**
                 * Resend an OTP to the current mobile number
                 *
                 * @returns {Promise<boolean>} True if the resend was successful
                 */
                resendOtp: async () => {
                    try {
                        const { mobile } = get()

                        if (!mobile) {
                            set({
                                error: 'No mobile number provided',
                                lastUpdated: Date.now(),
                            })
                            return false
                        }

                        set({ isLoading: true, error: null })

                        const response = await resendOTP(mobile)

                        // Check if the response indicates success (status code 200-299)
                        const isSuccess = response.statusCode >= 200 && response.statusCode < 300

                        if (!isSuccess) {
                            set({
                                isLoading: false,
                                error:
                                    response.error?.message ||
                                    response.message ||
                                    'Failed to resend OTP',
                                lastUpdated: Date.now(),
                            })
                            return false
                        }

                        set({ isLoading: false, lastUpdated: Date.now() })
                        return true
                    } catch (error) {
                        set({
                            isLoading: false,
                            error:
                                error instanceof Error
                                    ? error.message
                                    : 'An unknown error occurred',
                            lastUpdated: Date.now(),
                        })
                        return false
                    }
                },

                /**
                 * Log out the current user
                 * Calls the logout API to invalidate tokens on the server
                 *
                 * @returns Promise that resolves when logout is complete
                 */
                logout: async () => {
                    try {
                        set({ isLoading: true })

                        // Call the logout API to invalidate tokens on the server
                        await logoutUser()

                        // Clear local state
                        set({
                            isAuthenticated: false,
                            isLoading: false,
                            mobile: '',
                            user: null,
                            tokens: null,
                            lastUpdated: Date.now(),
                        })
                    } catch (error) {
                        console.error('Logout error:', error)

                        // Even if the API call fails, clear local state
                        set({
                            isAuthenticated: false,
                            isLoading: false,
                            mobile: '',
                            user: null,
                            tokens: null,
                            lastUpdated: Date.now(),
                        })
                    }
                },

                /**
                 * Refresh tokens using a refresh token
                 *
                 * @param refreshToken - The refresh token to use
                 * @returns Promise resolving to success status
                 */
                refreshTokens: async (refreshToken: string) => {
                    try {
                        const { refreshAccessToken } = await import('@/app/api/auth')
                        const response = await refreshAccessToken(refreshToken)

                        if (!response || !response.data?.accessToken) {
                            return false
                        }

                        const { accessToken, expiresIn } = response.data

                        // Update tokens in store
                        const expiresAt = expiresIn ? Date.now() + expiresIn * 1000 : undefined

                        set({
                            tokens: {
                                accessToken,
                                refreshToken, // Keep the same refresh token
                                expiresIn: expiresIn || 3600,
                                expiresAt,
                            },
                            lastUpdated: Date.now(),
                        })

                        return true
                    } catch (error) {
                        console.error('Token refresh error:', error)
                        return false
                    }
                },

                /**
                 * Update tokens in the store
                 *
                 * @param accessToken - New access token
                 * @param refreshToken - Optional new refresh token
                 * @param expiresIn - Optional token expiration in seconds
                 */
                updateTokens: (accessToken: string, refreshToken?: string, expiresIn?: number) => {
                    const { tokens } = get()

                    // Calculate expiration time
                    const expiresAt = expiresIn ? Date.now() + expiresIn * 1000 : undefined

                    set({
                        tokens: {
                            accessToken,
                            refreshToken: refreshToken || tokens?.refreshToken,
                            expiresIn: expiresIn || tokens?.expiresIn || 3600,
                            expiresAt,
                        },
                        lastUpdated: Date.now(),
                    })
                },

                /**
                 * Clear any error messages
                 */
                clearError: () => set({ error: null }),
            }),
            {
                name: 'auth-storage',
                storage: createJSONStorage(() => localStorage),
                partialize: (state) => ({
                    isAuthenticated: state.isAuthenticated,
                    mobile: state.mobile,
                    user: state.user,
                    tokens: state.tokens,
                }),
            },
        ),
    ),
)
