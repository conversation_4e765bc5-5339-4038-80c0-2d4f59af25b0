{"name": "uecom-frontend-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next build && next start", "lint": "next lint", "format": "prettier --write .", "e2e": "cypress open", "prepare": "husky install", "commit": "npx cz", "test": "jest --passWithNoTests", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "prettier:check": "prettier . --check", "prettier:write": "prettier . --write"}, "dependencies": {"@radix-ui/react-label": "^2.1.6", "axios": "^1.6.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.5.0", "lucide-react": "^0.483.0", "next": "15.1.7", "react": "^19.0.0", "react-dom": "^19.0.0", "react-responsive": "^10.0.1", "react-router-dom": "^7.4.0", "string-width": "4.2.3", "tailwind-merge": "^3.3.0", "util": "^0.12.5", "zustand": "^4.4.1"}, "devDependencies": {"@commitlint/cli": "^19.0.0", "@commitlint/config-conventional": "^19.0.0", "@eslint/eslintrc": "^3", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "commitizen": "^4.3.0", "cypress": "^13.1.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^9.20.1", "eslint-config-next": "15.1.7", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-tailwindcss": "^3.18.0", "husky": "^9.1.7", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.4.3", "node-fetch": "^3.3.2", "postcss": "^8", "prettier": "^3.5.3", "supertest": "^7.1.0", "tailwindcss": "^3.4.1", "text-encoding": "^0.7.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5", "whatwg-fetch": "^3.6.20"}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"**/*.{js,ts,tsx}": ["eslint --fix", "prettier --write"], "**/*.{json,md,css,scss}": ["prettier --write"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "browserslist": [">0.2%", "not dead", "not op_mini all"]}