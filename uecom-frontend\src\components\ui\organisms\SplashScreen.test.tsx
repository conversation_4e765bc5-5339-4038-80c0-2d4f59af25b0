import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useRouter } from 'next/navigation'
import SplashScreen from './SplashScreen'

// Mock the next/navigation router
jest.mock('next/navigation', () => ({
    useRouter: jest.fn(),
}))

// Mocking localStorage
const setItemMock = jest.fn()
const getItemMock = jest.fn()
Object.defineProperty(window, 'localStorage', {
    value: {
        setItem: setItemMock,
        getItem: getItemMock,
    },
})

describe('SplashScreen Component', () => {
    const mockRouter = { push: jest.fn() }

    beforeEach(() => {
        // Reset mocks
        jest.clearAllMocks()

        // Setup router mock
        ;(useRouter as jest.Mock).mockReturnValue(mockRouter)

        // Default localStorage behavior
        getItemMock.mockReturnValue(null)
    })

    const screens = [
        {
            id: 1,
            title: 'Book Service',
            description: 'Book a service with just a few taps',
            icon: '/icons/book-service.png',
        },
        {
            id: 2,
            title: 'Track Progress',
            description: 'Track the progress of your service in real-time',
            icon: '/icons/track-progress.png',
        },
        {
            id: 3,
            title: 'Get Support',
            description: '24/7 customer support for all your needs',
            icon: '/icons/support.png',
        },
    ]

    test('renders loading state when no screens are provided', () => {
        render(<SplashScreen screens={[]} />)

        const loadingElement = screen.getByRole('status')
        expect(loadingElement).toBeInTheDocument()
    })

    test('renders the first screen by default', () => {
        render(<SplashScreen screens={screens} />)

        expect(screen.getByText('Book Service')).toBeInTheDocument()
        expect(screen.getByText('Book a service with just a few taps')).toBeInTheDocument()
    })

    test('navigates to the next screen when next button is clicked', () => {
        render(<SplashScreen screens={screens} />)

        // Click the next button
        const nextButton = screen.getByLabelText('Next Screen')
        fireEvent.click(nextButton)

        // Check that we're on the second screen
        expect(screen.getByText('Track Progress')).toBeInTheDocument()
    })

    test('navigates to the previous screen when previous button is clicked', () => {
        render(<SplashScreen screens={screens} />)

        // Go to second screen
        const nextButton = screen.getByLabelText('Next Screen')
        fireEvent.click(nextButton)

        // Click the previous button
        const prevButton = screen.getByLabelText('Previous Screen')
        fireEvent.click(prevButton)

        // Check that we're back on the first screen
        expect(screen.getByText('Book Service')).toBeInTheDocument()
    })

    test('skips to login when skip button is clicked', () => {
        render(<SplashScreen screens={screens} />)

        // Click the skip button
        const skipButton = screen.getByLabelText('Skip Onboarding')
        fireEvent.click(skipButton)

        // Verify localStorage was updated
        expect(setItemMock).toHaveBeenCalledWith('is_splash_show_done', 'true')

        // Verify router navigation
        expect(mockRouter.push).toHaveBeenCalledWith('/auth')
    })

    test('shows login button when all screens have been viewed', async () => {
        // Mock that we're on the last screen
        jest.useFakeTimers()

        render(<SplashScreen screens={screens} />)

        // Navigate to the last screen
        const nextButton = screen.getByLabelText('Next Screen')
        fireEvent.click(nextButton)
        fireEvent.click(screen.getByLabelText('Next Screen'))

        // Advance timers to trigger the completion
        jest.advanceTimersByTime(3000)

        // Check for the login button
        expect(screen.getByText('Click here to Login')).toBeInTheDocument()

        // Cleanup
        jest.useRealTimers()
    })
})
