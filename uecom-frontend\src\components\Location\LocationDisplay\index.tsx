'use client'

import React, { memo, useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import LocationIcon from '../LocationIcon'
import { LocationDisplayProps } from '@/types/location'

/**
 * Component to display the fetched location information
 * Enhanced with accessibility attributes and memoization
 * Displays location for 5 seconds then redirects to discovery route
 */
const LocationDisplay: React.FC<LocationDisplayProps> = memo(({ address, postalCode }) => {
    const router = useRouter()
    const [countdown, setCountdown] = useState(3)
    const [redirecting, setRedirecting] = useState(false)

    useEffect(() => {
        // Set up countdown timer
        const timer = setInterval(() => {
            setCountdown((prev) => {
                if (prev <= 1) {
                    clearInterval(timer)
                    setRedirecting(true)
                    // Redirect after showing "Redirecting..." for a moment
                    setTimeout(() => {
                        router.push('/discovery')
                    }, 200)
                    return 0
                }
                return prev - 1
            })
        }, 1000)

        // Clean up timer on unmount
        return () => clearInterval(timer)
    }, [router])

    return (
        <div
            className="flex flex-col items-center justify-center min-h-screen bg-white"
            role="region"
            aria-label="Location Information"
        >
            <div className="mb-6">
                <LocationIcon animate={false} size="md" />
            </div>
            <div className="text-center px-4 max-w-md">
                <h1 className="text-gray-800 font-medium text-lg mb-2" tabIndex={0}>
                    {address}
                </h1>
                <p
                    className="text-gray-500 text-sm mb-1"
                    aria-label={postalCode ? `Pincode: ${postalCode}` : 'Pincode not available'}
                    tabIndex={0}
                >
                    {postalCode ? `Pincode: ${postalCode}` : 'Pincode not available'}
                </p>
                <p className="text-gray-500 text-sm mb-4" tabIndex={0}>
                    Location data is accurate within a few meters
                </p>

                {/* Countdown and redirect message */}
                <div className="mt-4 text-blue-600 font-medium" aria-live="polite">
                    {redirecting
                        ? 'Redirecting to discovery page...'
                        : `Redirecting to discovery page in ${countdown} seconds...`}
                </div>
            </div>
        </div>
    )
})

// Add display name for better debugging
LocationDisplay.displayName = 'LocationDisplay'

export default LocationDisplay
