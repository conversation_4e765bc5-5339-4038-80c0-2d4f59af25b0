import React, { memo } from 'react'
import { LocationIconProps } from '@/types/location'

/**
 * A reusable location pin icon component
 * Enhanced with accessibility attributes and memoization
 */
const LocationIcon: React.FC<LocationIconProps> = memo(({ animate = false, size = 'md' }) => {
    // Size mappings
    const sizeMap = {
        sm: {
            container: 'w-16 h-16',
            inner: 'w-8 h-8',
            icon: 'w-5 h-5',
        },
        md: {
            container: 'w-20 h-20',
            inner: 'w-10 h-10',
            icon: 'w-6 h-6',
        },
        lg: {
            container: 'w-24 h-24',
            inner: 'w-12 h-12',
            icon: 'w-7 h-7',
        },
    }

    const { container, inner, icon } = sizeMap[size]

    return (
        <div className={`relative ${container}`} role="img" aria-label="Location pin icon">
            {/* Outer glow/shadow */}
            <div className="absolute inset-0 rounded-full bg-blue-100 opacity-30"></div>

            {/* Animated ring - only shown when animate is true */}
            {animate && (
                <div
                    className="absolute inset-2 rounded-full border-2 border-blue-300 opacity-50 animate-ping"
                    aria-hidden="true"
                ></div>
            )}

            {/* Inner circle with location pin */}
            <div className="absolute inset-0 flex items-center justify-center">
                <div
                    className={`${inner} rounded-full bg-white shadow-md flex items-center justify-center`}
                >
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        className={`${icon} text-blue-500`}
                        fill="currentColor"
                        aria-hidden="true"
                        focusable="false"
                    >
                        <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z" />
                    </svg>
                </div>
            </div>
        </div>
    )
})

// Add display name for better debugging
LocationIcon.displayName = 'LocationIcon'

export default LocationIcon
