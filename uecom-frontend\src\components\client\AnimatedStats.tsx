'use client'

import React, { useState, useEffect } from 'react'
import { StatsSection } from '../../types'
import { useDeviceContext } from '../../providers/DeviceProvider'

interface AnimatedStatsProps {
    data: StatsSection
}

const AnimatedStats: React.FC<AnimatedStatsProps> = ({ data }) => {
    const { value } = data
    const { stats } = value
    const { isMobile, isTablet, isMounted } = useDeviceContext()
    const [counts, setCounts] = useState<{ [key: string]: number }>({})

    useEffect(() => {
        if (!stats?.length) return

        const timers: NodeJS.Timeout[] = []

        stats.forEach((stat) => {
            // Extract numeric value for animation
            const numericValue = parseFloat(stat.value.replace(/[^0-9.]/g, ''))
            const isMillions = stat.value.includes('M')
            const endValue = isMillions ? numericValue : parseInt(stat.value.replace(/\D/g, ''))

            let start = 0
            const duration = 2000 // 2 seconds
            const increment = endValue / (duration / 16) // 60 FPS

            const timer = setInterval(() => {
                start += increment
                let currentCount = start

                if (currentCount >= endValue) {
                    currentCount = endValue
                    clearInterval(timer)
                }

                setCounts((prev) => ({
                    ...prev,
                    [stat.label]: currentCount,
                }))
            }, 16)

            timers.push(timer)
        })

        return () => {
            timers.forEach((timer) => clearInterval(timer))
        }
    }, [stats])

    const formatValue = (stat: any, count: number) => {
        if (stat.value.includes('M')) {
            return `${count.toFixed(1)}M+`
        }
        if (count >= 1000) {
            return `${Math.round(count).toLocaleString()}+`
        }
        return `${count.toFixed(0)}+`
    }

    if (!stats?.length) {
        return null
    }

    const MobileView = () => (
        <div className="px-4 mt-10 block sm:block md:hidden">
            <div className="p-[1px] rounded-3xl bg-gradient-to-r from-blue-700 via-gray-400 to-blue-600 relative">
                <div className="bg-white rounded-3xl p-6 relative flex justify-center">
                    {/* Sparkle icons */}
                    <div className="absolute -top-3 -right-4">
                        <img src={data.iconImage} alt="sparkle" className="w-16 h-16" />
                    </div>

                    <div className="grid grid-cols-2 gap-6 text-center">
                        {stats.map((stat) => (
                            <div key={`stat-${stat.label}`}>
                                <div className="flex flex-col justify-center text-2xl font-bold mb-1 bg-gradient-to-r from-gray-700 via-blue-700 to-blue-700 bg-clip-text text-transparent">
                                    {counts[stat.label] !== undefined
                                        ? formatValue(stat, counts[stat.label])
                                        : stat.value}
                                </div>
                                <div className="text-sm text-gray-600 leading-tight">
                                    {stat.label}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    )

    const TabletView = () => (
        <div className="px-6 mt-10 hidden md:block lg:hidden">
            <div className="p-[1px] rounded-3xl bg-gradient-to-r from-blue-700 via-gray-400 to-blue-600 relative">
                <div className="bg-blue-50 rounded-3xl p-8 relative">
                    {/* Sparkle icons */}
                    <div className="absolute -top-3 -right-4">
                        <img src={data.iconImage} alt="sparkle" className="w-16 h-16" />
                    </div>

                    <div className="grid grid-cols-3 gap-6">
                        {stats.map((stat) => (
                            <div key={`stat-${stat.label}`} className="text-center">
                                <div className="text-3xl font-bold mb-1 bg-gradient-to-r from-gray-700 via-blue-700 to-blue-700 bg-clip-text text-transparent">
                                    {counts[stat.label] !== undefined
                                        ? formatValue(stat, counts[stat.label])
                                        : stat.value}
                                </div>
                                <div className="text-sm text-gray-600 leading-tight">
                                    {stat.label}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    )

    const DesktopView = () => (
        <div className="px-10 mt-10 hidden lg:block">
            <div className="p-[1px] rounded-3xl bg-gradient-to-r from-blue-700 via-gray-400 to-blue-600 relative">
                <div className="bg-blue-50 rounded-3xl p-12 relative">
                    {/* Sparkle icons */}
                    <div className="absolute -top-3 -right-4">
                        <img src={data.iconImage} alt="sparkle" className="w-16 h-16" />
                    </div>

                    <div className="grid grid-cols-4 gap-6">
                        {stats.map((stat) => (
                            <div key={`stat-${stat.label}`} className="text-center">
                                <div className="text-4xl font-bold mb-1 bg-gradient-to-r from-gray-700 via-blue-700 to-blue-700 bg-clip-text text-transparent">
                                    {counts[stat.label] !== undefined
                                        ? formatValue(stat, counts[stat.label])
                                        : stat.value}
                                </div>
                                <div className="text-sm text-gray leading-tight">{stat.label}</div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    )

    if (!isMounted) return null
    if (isMobile) return <MobileView />
    if (isTablet) return <TabletView />
    return <DesktopView />
}

export default AnimatedStats
