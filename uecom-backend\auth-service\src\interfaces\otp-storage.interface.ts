/**
 * OTP Storage Interface
 *
 * This interface defines the contract for any OTP storage implementation.
 * It follows the Interface Segregation Principle by defining only the methods
 * needed for OTP storage and retrieval.
 *
 * @module interfaces/otp-storage
 */

export interface IOtpStorage {
    /**
     * Stores an OTP for a given mobile number with an optional expiration time
     *
     * @param {string} mobile - The mobile number
     * @param {string} otp - The OTP to store
     * @param {number} [expiryMinutes=15] - Number of minutes until the OTP expires
     * @returns {Promise<void>}
     */
    storeOtp(
        mobile: string,
        otp: string,
        expiryMinutes?: number
    ): Promise<void>;

    /**
     * Retrieves an OTP for a given mobile number if it hasn't expired
     *
     * @param {string} mobile - The mobile number
     * @returns {Promise<string | undefined>} The stored OTP or undefined if not found or expired
     */
    getOtp(mobile: string): Promise<string | undefined>;

    /**
     * Removes an OTP for a given mobile number
     *
     * @param {string} mobile - The mobile number
     * @returns {Promise<void>}
     */
    removeOtp(mobile: string): Promise<void>;

    /**
     * Cleans up expired OTPs
     * This method should be called periodically to remove expired OTPs
     * Note: For Redis implementations, this may be a no-op as Redis handles expiration automatically
     */
    cleanupExpiredOtps(): void;
}
