import { Request, Response } from 'express';
import { pageDataController } from '../../src/controllers/page-data';
import { pageDataService } from '../../src/services/page-data';
import { pageData } from '../../src/data/pageData';

// Mock the page data service
jest.mock('../../src/services/page-data');

describe('Page Data Controller', () => {
    let mockRequest: Partial<Request>;
    let mockResponse: Partial<Response>;
    let jsonSpy: jest.Mock;
    let statusSpy: jest.Mock;

    beforeEach(() => {
        jsonSpy = jest.fn();
        statusSpy = jest.fn().mockReturnThis();
        
        mockRequest = {};
        mockResponse = {
            status: statusSpy,
            json: jsonSpy,
        };

        // Reset all mocks
        jest.clearAllMocks();
    });

    describe('getPageData', () => {
        it('should return page data for the specified page', async () => {
            // Mock the service response
            (pageDataService.getPageData as jest.Mock).mockResolvedValue(pageData);
            
            // Set up request params
            mockRequest.params = { pageName: 'home' };
            
            // Call the controller
            await pageDataController.getPageData(mockRequest as Request, mockResponse as Response);
            
            // Verify the service was called with the correct parameter
            expect(pageDataService.getPageData).toHaveBeenCalledWith('home');
            
            // Verify the response
            expect(statusSpy).toHaveBeenCalledWith(200);
            expect(jsonSpy).toHaveBeenCalledWith(pageData);
        });

        it('should return 400 if pageName is missing', async () => {
            // Set up request with missing pageName
            mockRequest.params = {};
            
            // Call the controller
            await pageDataController.getPageData(mockRequest as Request, mockResponse as Response);
            
            // Verify the response
            expect(statusSpy).toHaveBeenCalledWith(400);
            expect(jsonSpy).toHaveBeenCalledWith({
                error: 'Missing required parameter: pageName',
                message: 'Page name is required'
            });
        });

        it('should return 500 if an error occurs', async () => {
            // Mock the service to throw an error
            (pageDataService.getPageData as jest.Mock).mockRejectedValue(new Error('Test error'));
            
            // Set up request params
            mockRequest.params = { pageName: 'home' };
            
            // Call the controller
            await pageDataController.getPageData(mockRequest as Request, mockResponse as Response);
            
            // Verify the response
            expect(statusSpy).toHaveBeenCalledWith(500);
            expect(jsonSpy).toHaveBeenCalledWith({
                error: 'Internal server error',
                message: 'Failed to fetch page data'
            });
        });
    });

    describe('getAllPageData', () => {
        it('should return all page data', async () => {
            // Mock the service response
            (pageDataService.getPageData as jest.Mock).mockResolvedValue(pageData);
            
            // Call the controller
            await pageDataController.getAllPageData(mockRequest as Request, mockResponse as Response);
            
            // Verify the service was called with the correct parameter
            expect(pageDataService.getPageData).toHaveBeenCalledWith('home');
            
            // Verify the response
            expect(statusSpy).toHaveBeenCalledWith(200);
            expect(jsonSpy).toHaveBeenCalledWith(pageData);
        });

        it('should return 500 if an error occurs', async () => {
            // Mock the service to throw an error
            (pageDataService.getPageData as jest.Mock).mockRejectedValue(new Error('Test error'));
            
            // Call the controller
            await pageDataController.getAllPageData(mockRequest as Request, mockResponse as Response);
            
            // Verify the response
            expect(statusSpy).toHaveBeenCalledWith(500);
            expect(jsonSpy).toHaveBeenCalledWith({
                error: 'Internal server error',
                message: 'Failed to fetch page data'
            });
        });
    });
});
