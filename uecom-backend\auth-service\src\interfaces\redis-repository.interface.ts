/**
 * Redis Repository Interface
 *
 * This interface defines the contract for Redis repository implementations.
 * It provides methods for interacting with Redis.
 *
 * @module interfaces/redis-repository
 */

export interface IRedisRepository {
    /**
     * Checks if the Redis connection is ready
     *
     * @returns {Promise<boolean>} True if the connection is ready, false otherwise
     */
    isReady(): Promise<boolean>;

    /**
     * Gets a value from Redis
     *
     * @param {string} key - The key to get
     * @returns {Promise<string | null>} The value or null if not found
     */
    get(key: string): Promise<string | null>;

    /**
     * Sets a value in Redis with optional expiration
     *
     * @param {string} key - The key to set
     * @param {string} value - The value to set
     * @param {number} [expirySeconds] - Optional expiration time in seconds
     * @returns {Promise<void>}
     */
    set(key: string, value: string, expirySeconds?: number): Promise<void>;

    /**
     * Deletes a key from Redis
     *
     * @param {string} key - The key to delete
     * @returns {Promise<boolean>} True if the key was deleted, false otherwise
     */
    del(key: string): Promise<boolean>;

    /**
     * Checks if a key exists in Redis
     *
     * @param {string} key - The key to check
     * @returns {Promise<boolean>} True if the key exists, false otherwise
     */
    exists(key: string): Promise<boolean>;
}
