/**
 * API Version Middleware
 *
 * This middleware handles API versioning for the application.
 * It supports version specification via URL path, query parameter, or header.
 *
 * @module middlewares/api-version
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

/**
 * Supported API versions
 */
export enum ApiVersion {
    V1 = 'v1',
    V2 = 'v2',
}

/**
 * Default API version
 */
export const DEFAULT_API_VERSION = ApiVersion.V1;

/**
 * API version header name
 */
export const API_VERSION_HEADER = 'X-API-Version';

/**
 * API version query parameter name
 */
export const API_VERSION_QUERY_PARAM = 'version';

// Extend Express Request interface to include apiVersion property
declare global {
    namespace Express {
        interface Request {
            apiVersion: ApiVersion;
        }
    }
}

/**
 * Middleware to handle API versioning
 *
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {NextFunction} next - Express next function
 */
export const apiVersionMiddleware = (
    req: Request,
    res: Response,
    next: NextFunction
): void => {
    let version: ApiVersion | undefined;

    // Check URL path for version (e.g., /v1/auth)
    const pathParts = req.path.split('/');
    if (pathParts.length > 1 && pathParts[1].match(/^v[0-9]+$/)) {
        const pathVersion = pathParts[1].toLowerCase();
        if (Object.values(ApiVersion).includes(pathVersion as ApiVersion)) {
            version = pathVersion as ApiVersion;
        }
    }

    // If not found in path, check header
    if (!version) {
        const headerVersion = req.headers[
            API_VERSION_HEADER.toLowerCase()
        ] as string;
        if (
            headerVersion &&
            Object.values(ApiVersion).includes(headerVersion as ApiVersion)
        ) {
            version = headerVersion as ApiVersion;
        }
    }

    // If not found in header, check query parameter
    if (!version) {
        const queryVersion = req.query[API_VERSION_QUERY_PARAM] as string;
        if (
            queryVersion &&
            Object.values(ApiVersion).includes(queryVersion as ApiVersion)
        ) {
            version = queryVersion as ApiVersion;
        }
    }

    // If still not found, use default version
    if (!version) {
        version = DEFAULT_API_VERSION;
    }

    // Attach API version to request object
    req.apiVersion = version;

    // Add API version to response headers
    res.setHeader(API_VERSION_HEADER, version);

    // Log API version for debugging
    logger.debug(`API version: ${version}`, null, req.requestId);

    next();
};
