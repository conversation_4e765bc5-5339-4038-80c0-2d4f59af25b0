/**
 * Authentication Service Entry Point
 *
 * This file initializes and configures the Express server for the Auth Service,
 * which handles user authentication via OTP for the WIFY ECOM application.
 *
 * @module server
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import cookieParser from 'cookie-parser';
import dotenv from 'dotenv';
import authRoutes from './routes/auth';
import healthRoutes from './routes/health';
import { initRateLimiters, apiLimiter } from './middlewares/rate-limiter';
import { setupContainer } from './di/setup';
import { container } from './di/container';
import { SERVICE_TOKENS } from './di/tokens';
import { IConfigService } from './interfaces/config-service.interface';
import { globalErrorHandler } from './utils/errorHandler';
import { requestIdMiddleware } from './middlewares/request-id.middleware';
import { apiVersionMiddleware } from './middlewares/api-version.middleware';
import { csrfSetToken, csrfProtection } from './middlewares/csrf.middleware';
import {
    DatabaseHealthService,
    createDatabaseHealthService,
} from './services/database-health.service';
import { DatabaseService } from './services/database.service';

// Load environment variables from .env file
dotenv.config();

// Import logger
import { logger } from './utils/logger';

// Log startup information
logger.info(
    `🚀 Starting Auth Service in ${process.env.NODE_ENV || 'development'} mode...`
);

// Set up dependency injection container
setupContainer();

// Initialize rate limiters after DI container is set up
initRateLimiters();

// Initialize Express application
const app = express();

// Apply comprehensive security headers using Helmet
app.use(
    helmet({
        contentSecurityPolicy: {
            directives: {
                defaultSrc: ["'self'"],
                scriptSrc: ["'self'"],
                styleSrc: ["'self'", "'unsafe-inline'"],
                imgSrc: ["'self'", 'data:'],
                connectSrc: ["'self'"],
                fontSrc: ["'self'"],
                objectSrc: ["'none'"],
                mediaSrc: ["'self'"],
                frameSrc: ["'none'"],
            },
        },
        xssFilter: true,
        noSniff: true,
        hsts: {
            maxAge: 31536000, // 1 year in seconds
            includeSubDomains: true,
            preload: true,
        },
        hidePoweredBy: true,
        frameguard: { action: 'deny' },
    })
);

// Parse JSON request bodies with size limit to prevent DoS attacks
app.use(express.json({ limit: '100kb' }));

// Parse cookies
app.use(cookieParser());

// Add request ID middleware for request tracing
app.use(requestIdMiddleware);

// Add API versioning middleware
app.use(apiVersionMiddleware);

// Configure CORS with specific origins, methods, and headers
app.use(
    cors({
        origin: (origin, callback) => {
            // Get config service from container
            const configService = container.resolve<IConfigService>(
                SERVICE_TOKENS.CONFIG_SERVICE
            );

            // Get allowed origins from config
            const allowedOrigins = configService.getAllowedOrigins();

            // Allow requests with no origin (like mobile apps or curl requests)
            if (!origin) return callback(null, true);

            // Check if the origin is allowed
            if (
                allowedOrigins.indexOf(origin) !== -1 ||
                process.env.NODE_ENV === 'development'
            ) {
                callback(null, true);
            } else {
                callback(
                    new Error('CORS policy violation: Origin not allowed'),
                    false
                );
            }
        },
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
        exposedHeaders: ['Set-Cookie'],
        credentials: true, // Allow cookies to be sent with requests
        maxAge: 86400, // 24 hours in seconds - how long the results of a preflight request can be cached
        optionsSuccessStatus: 204,
    })
);

// Add request logging with enhanced logger
app.use((req, res, next) => {
    const start = Date.now();

    // Log when the request completes
    res.on('finish', () => {
        const duration = Date.now() - start;
        const logMessage = `${req.method} ${req.originalUrl} ${res.statusCode} ${duration}ms`;

        // Log at appropriate level based on status code
        if (res.statusCode >= 500) {
            logger.error(logMessage);
        } else if (res.statusCode >= 400) {
            logger.warn(logMessage);
        } else {
            logger.info(logMessage);
        }
    });

    next();
});

// Apply global rate limiting to all requests
app.use(apiLimiter);

// Add CSRF protection
// app.use(csrfSetToken);
// app.use(csrfProtection);

// Register health check routes
app.use('/health', healthRoutes);

// Register authentication routes
app.use('/v1/auth', authRoutes);

// Add global error handler
app.use(globalErrorHandler);

// Get config service from container
const configService = container.resolve<IConfigService>(
    SERVICE_TOKENS.CONFIG_SERVICE
);

// Get port from configuration or use default
const PORT = configService.getPort();

// We'll initialize database health monitoring after the server starts
// to avoid async initialization issues

// Start the server
app.listen(PORT, async () => {
    logger.info(
        `🚀 Authentication service running on: http://localhost:${PORT}`
    );
    logger.info(`🔒 Security measures enabled: Helmet, CORS, Rate Limiting`);
    logger.info(`🧩 SOLID principles and design patterns implemented`);

    try {
        // Initialize database health service
        const databaseService = container.resolve<DatabaseService>(
            SERVICE_TOKENS.DATABASE_SERVICE
        );
        const prisma = await databaseService.getPrismaClient();

        // Create database health service directly
        const databaseHealthService = createDatabaseHealthService(prisma, {
            failureThreshold: 3,
            resetTimeout: 30000,
            checkInterval: 60000,
        });

        // Start monitoring
        databaseHealthService.startMonitoring();

        // Listen for database health events
        databaseHealthService.on('healthCheck', (result: any) => {
            if (result.status === 'DOWN') {
                logger.error(
                    `Database health check failed: ${JSON.stringify(result.details)}`
                );
            } else if (result.status === 'DEGRADED') {
                logger.warn(
                    `Database in degraded state: ${JSON.stringify(result.details)}`
                );
            }
        });

        databaseHealthService.on('circuitOpen', () => {
            logger.warn(
                'Database circuit breaker opened - database operations will fail fast'
            );
        });

        databaseHealthService.on('circuitHalfOpen', () => {
            logger.info(
                'Database circuit breaker half-open - testing database connectivity'
            );
        });

        databaseHealthService.on('circuitClosed', () => {
            logger.info(
                'Database circuit breaker closed - database operations back to normal'
            );
        });

        logger.info(
            `🔍 Database health monitoring active with circuit breaker pattern`
        );
    } catch (error) {
        logger.error('Failed to initialize database health monitoring:', error);
    }
});
