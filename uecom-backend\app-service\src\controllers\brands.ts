/**
 * Brands Controller
 *
 * This controller handles HTTP requests related to brands.
 * It follows the Single Responsibility Principle by focusing only on brand-related endpoints.
 */

import { Request, Response, RequestHandler } from 'express';
import { brandsService } from '../services/brands.service';
import { logger } from '../utils/logger';

/**
 * Export the controller functions
 */
export const brandsController = {
    /**
     * Get all brands
     */
    getAllBrands: (async (_req: Request, res: Response) => {
        try {
            logger.info('Received request for all brands');

            // Get brands data from service
            const data = await brandsService.getAllBrands();

            // Return brands data
            res.status(200).json(data);
        } catch (error) {
            logger.error('Error in getAllBrands controller:', error);
            res.status(500).json({
                error: 'Internal server error',
                message: 'Failed to fetch brands data',
            });
        }
    }) as RequestHandler,
};
