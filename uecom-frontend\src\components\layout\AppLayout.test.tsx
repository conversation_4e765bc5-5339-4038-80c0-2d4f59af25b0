import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import AppLayout from './AppLayout'
import { DeviceType } from '@/lib/constants/breakpoints'

// Mock the components used by AppLayout
jest.mock('@/components/sections/Header', () => {
    return {
        __esModule: true,
        default: jest.fn(({ data }) => <header data-testid="mock-header">Header Component</header>),
    }
})

jest.mock('@/components/sections/Footer', () => {
    return {
        __esModule: true,
        default: jest.fn(({ data }) => <footer data-testid="mock-footer">Footer Component</footer>),
    }
})

jest.mock('@/components/navigation/mobileBottomNavigation', () => {
    return {
        __esModule: true,
        default: jest.fn(() => <nav data-testid="mock-mobile-nav">Mobile Navigation</nav>),
    }
})

jest.mock('./SectionRenderer', () => {
    return {
        SectionRenderer: jest.fn(({ section, index }) => (
            <div data-testid={`section-${section.type}-${index}`}>{section.type} Section</div>
        )),
    }
})

describe('AppLayout Component', () => {
    const mockSections = [
        {
            type: 'header',
            label: 'Header',
            value: {
                location: 'Test Location',
                Image: 'test-image.png',
                logoText: 'Test Logo',
                navLinks: [],
                businessLink: 'Business',
            },
        },
        {
            type: 'content',
            label: 'Content',
            value: {
                title: 'Test Content',
            },
        },
        {
            type: 'footer',
            label: 'Footer',
            value: {
                tagline: 'Test Tagline',
                address: 'Test Address',
                phone: 'Test Phone',
                email: '<EMAIL>',
                legalTitle: 'Legal',
                companyTitle: 'Company',
                connectTitle: 'Connect',
                legalLinks: [],
                companyLinks: [],
                socials: [],
                copyright: 'Copyright',
                companyName: 'Test Company',
            },
        },
    ]

    it('renders with static children when no sections are provided', () => {
        render(
            <AppLayout>
                <div data-testid="test-child">Test Child Content</div>
            </AppLayout>,
        )

        // Check that the child content is rendered
        expect(screen.getByTestId('test-child')).toBeInTheDocument()

        // Check that header and footer are rendered
        expect(screen.getByTestId('mock-header')).toBeInTheDocument()
        expect(screen.getByTestId('mock-footer')).toBeInTheDocument()

        // Check that mobile navigation is rendered
        expect(screen.getByTestId('mock-mobile-nav')).toBeInTheDocument()
    })

    it('renders with dynamic sections when sections are provided', () => {
        render(<AppLayout sections={mockSections} />)

        // Check that the sections are rendered
        expect(screen.getByTestId('section-header-0')).toBeInTheDocument()
        expect(screen.getByTestId('section-content-0')).toBeInTheDocument()
        expect(screen.getByTestId('section-footer-2')).toBeInTheDocument()
    })

    it('does not render mobile navigation when showMobileNav is false', () => {
        render(<AppLayout showMobileNav={false}>Test Content</AppLayout>)

        // Check that mobile navigation is not rendered
        expect(screen.queryByTestId('mock-mobile-nav')).not.toBeInTheDocument()
    })
})
