import { Request, Response } from 'express';
import * as authController from '../../src/controllers/auth';
import { authService } from '../../src/services/auth';

jest.mock('../../src/services/auth');

describe('Auth Controller', () => {
    let req: Partial<Request>;
    let res: Partial<Response>;
    let json: jest.Mock;
    let send: jest.Mock;

    beforeEach(() => {
        req = {};
        json = jest.fn();
        send = jest.fn();
        res = {
            status: jest.fn().mockReturnThis(),
            json,
            send,
        };
    });

    describe('healthCheck', () => {
        it('should return a 200 status with health check response', async () => {
            const healthCheckResponse = { status: 'ok' };

            (authService.healthCheck as jest.Mock).mockReturnValue(
                healthCheckResponse
            );

            await authController.healthCheck(req as Request, res as Response);

            expect(res.status).toHaveBeenCalledWith(200);
            expect(res.send).toHaveBeenCalledWith(healthCheckResponse);
        });
    });

    describe('requestOtp', () => {
        it('should return a 200 status with OTP request response', async () => {
            const mobile = '**********';
            const otpResponse = { message: 'OTP sent successfully' };

            (authService.requestOtp as jest.Mock).mockReturnValue(otpResponse);

            req.body = { mobile };

            await authController.requestOtp(req as Request, res as Response);

            expect(res.status).toHaveBeenCalledWith(200);
            expect(res.json).toHaveBeenCalledWith(otpResponse);
            expect(authService.requestOtp).toHaveBeenCalledWith(mobile);
        });
    });

    describe('verifyOtp', () => {
        it('should return a 200 status with OTP verification response', async () => {
            const mobile = '**********';
            const otp = '123456';
            const verifyResponse = { message: 'OTP verified successfully' };

            (authService.verifyOtp as jest.Mock).mockReturnValue(
                verifyResponse
            );

            req.body = { mobile, otp };

            await authController.verifyOtp(req as Request, res as Response);

            expect(res.status).toHaveBeenCalledWith(200);
            expect(res.json).toHaveBeenCalledWith(verifyResponse);
            expect(authService.verifyOtp).toHaveBeenCalledWith(mobile, otp);
        });
    });

    describe('logout', () => {
        it('should clear the refresh token cookie and return success response', () => {
            // Mock the clearCookie method
            const clearCookie = jest.fn().mockReturnThis();
            res.clearCookie = clearCookie;

            authController.logout(req as Request, res as Response);

            // Verify clearCookie was called with the correct parameters
            expect(clearCookie).toHaveBeenCalledWith(
                'refreshToken',
                expect.objectContaining({
                    httpOnly: true,
                    path: '/v1/auth/refresh-token',
                })
            );

            // Verify the response
            expect(res.status).toHaveBeenCalledWith(200);
            expect(res.json).toHaveBeenCalledWith(
                expect.objectContaining({
                    statusCode: 200,
                    message: 'Logged out successfully',
                })
            );
        });
    });
});
