'use client'

/**
 * Loading Spinner Component
 *
 * A reusable loading spinner component with customizable size and color.
 *
 * @module components/ui/LoadingSpinner
 */

interface LoadingSpinnerProps {
    size?: 'sm' | 'md' | 'lg'
    color?: 'primary' | 'secondary' | 'white'
    className?: string
}

/**
 * Loading Spinner Component
 *
 * @param props - Component props
 * @returns JSX element
 */
export default function LoadingSpinner({
    size = 'md',
    color = 'primary',
    className = '',
}: LoadingSpinnerProps) {
    // Size classes
    const sizeClasses = {
        sm: 'w-4 h-4 border-2',
        md: 'w-8 h-8 border-3',
        lg: 'w-12 h-12 border-4',
    }

    // Color classes
    const colorClasses = {
        primary: 'border-blue-600 border-t-transparent',
        secondary: 'border-gray-600 border-t-transparent',
        white: 'border-white border-t-transparent',
    }

    return (
        <div className="flex justify-center items-center">
            <div
                className={`
                    ${sizeClasses[size]} 
                    ${colorClasses[color]} 
                    ${className}
                    rounded-full animate-spin
                `}
                role="status"
                aria-label="Loading"
            />
        </div>
    )
}
