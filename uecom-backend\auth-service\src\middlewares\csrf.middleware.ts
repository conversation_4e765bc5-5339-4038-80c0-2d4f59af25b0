/**
 * CSRF Protection Middleware
 *
 * This middleware implements CSRF protection for the API.
 * It uses the double submit cookie pattern to prevent CSRF attacks.
 *
 * @module middlewares/csrf
 */

import { Request, Response, NextFunction } from 'express';
import crypto from 'crypto';
import { handleAuthError, ErrorCode } from '../utils/errorHandler';
import { logger } from '../utils/logger';

// Constants
const CSRF_COOKIE_NAME = 'XSRF-TOKEN';
const CSRF_HEADER_NAME = 'X-XSRF-TOKEN';
const CSRF_TOKEN_LENGTH = 32;

/**
 * Generates a random CSRF token
 *
 * @returns {string} A random CSRF token
 */
export const generateCsrfToken = (): string => {
    return crypto.randomBytes(CSRF_TOKEN_LENGTH / 2).toString('hex');
};

/**
 * Sets a CSRF token cookie
 *
 * @param {Response} res - Express response object
 * @returns {string} The generated CSRF token
 */
export const setCsrfToken = (res: Response): string => {
    const token = generateCsrfToken();

    res.cookie(CSRF_COOKIE_NAME, token, {
        httpOnly: false, // Must be accessible from JavaScript
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
    });

    return token;
};

/**
 * Middleware to set a CSRF token cookie
 *
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {NextFunction} next - Express next function
 */
export const csrfSetToken = (
    req: Request,
    res: Response,
    next: NextFunction
): void => {
    // Only set token if it doesn't exist
    if (!req.cookies[CSRF_COOKIE_NAME]) {
        setCsrfToken(res);
    }

    next();
};

/**
 * Middleware to verify CSRF token
 * Only applies to state-changing methods (POST, PUT, DELETE, PATCH)
 *
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {NextFunction} next - Express next function
 */
export const csrfProtection = (
    req: Request,
    res: Response,
    next: NextFunction
): void => {
    // Skip for non-state-changing methods
    const stateChangingMethods = ['POST', 'PUT', 'DELETE', 'PATCH'];
    if (!stateChangingMethods.includes(req.method)) {
        return next();
    }

    // Skip for development environment if configured
    if (
        process.env.DISABLE_CSRF_IN_DEV === 'true' &&
        process.env.NODE_ENV === 'development'
    ) {
        logger.warn('CSRF protection disabled in development mode');
        return next();
    }

    // Get token from cookie and header
    const cookieToken = req.cookies[CSRF_COOKIE_NAME];
    const headerToken = req.headers[CSRF_HEADER_NAME.toLowerCase()] as string;

    // Verify tokens exist and match
    if (!cookieToken || !headerToken) {
        logger.warn('CSRF token missing', {
            hasCookieToken: !!cookieToken,
            hasHeaderToken: !!headerToken,
        });
        return handleAuthError(
            res,
            'CSRF token missing',
            ErrorCode.CSRF_TOKEN_MISSING
        );
    }

    if (cookieToken !== headerToken) {
        logger.warn('CSRF token mismatch');
        return handleAuthError(
            res,
            'CSRF token invalid',
            ErrorCode.CSRF_TOKEN_INVALID
        );
    }

    // Refresh token on successful verification
    setCsrfToken(res);

    next();
};
