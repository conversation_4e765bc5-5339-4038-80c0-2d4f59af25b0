/**
 * Request ID Middleware
 *
 * This middleware adds a unique request ID to each incoming request.
 * It helps with request tracing and debugging across microservices.
 *
 * @module middlewares/request-id
 */

import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';

// Extend Express Request interface to include requestId property
declare global {
    namespace Express {
        interface Request {
            requestId?: string;
        }
    }
}

/**
 * Request ID header name
 */
export const REQUEST_ID_HEADER = 'X-Request-ID';

/**
 * Middleware to add a unique request ID to each request
 *
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {NextFunction} next - Express next function
 */
export const requestIdMiddleware = (
    req: Request,
    res: Response,
    next: NextFunction
): void => {
    // Use existing request ID from header if present, otherwise generate a new one
    const requestId =
        (req.headers[REQUEST_ID_HEADER.toLowerCase()] as string) || uuidv4();

    // Attach request ID to request object
    req.requestId = requestId;

    // Add request ID to response headers
    res.setHeader(REQUEST_ID_HEADER, requestId);

    next();
};
