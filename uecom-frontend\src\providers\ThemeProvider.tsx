'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'

// Define theme types
type Theme = 'light' | 'dark' | 'system'

// Define theme context type
interface ThemeContextType {
    theme: Theme
    setTheme: (theme: Theme) => void
}

// Create context with default values
const ThemeContext = createContext<ThemeContextType>({
    theme: 'system',
    setTheme: () => null,
})

// Custom hook for using theme context
export const useTheme = () => useContext(ThemeContext)

// Theme provider props
interface ThemeProviderProps {
    children: ReactNode
    defaultTheme?: Theme
    storageKey?: string
}

/**
 * Theme provider component that handles theme switching
 * Supports light, dark, and system themes
 * Persists theme preference in localStorage
 *
 * @param children - Child components
 * @param defaultTheme - Default theme to use
 * @param storageKey - Key to use for localStorage
 *
 * @example
 * ```tsx
 * <ThemeProvider defaultTheme="system">
 *   <App />
 * </ThemeProvider>
 * ```
 */
export function ThemeProvider({
    children,
    defaultTheme = 'system',
    storageKey = 'theme',
    ...props
}: ThemeProviderProps) {
    // Initialize theme state
    const [theme, setTheme] = useState<Theme>(defaultTheme)

    // Initialize theme from localStorage on mount
    useEffect(() => {
        const savedTheme = localStorage.getItem(storageKey) as Theme | null

        if (savedTheme) {
            setTheme(savedTheme)
        } else if (defaultTheme === 'system') {
            // If no saved theme and default is system, check system preference
            const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches
                ? 'dark'
                : 'light'
            setTheme(systemTheme)
        }
    }, [defaultTheme, storageKey])

    // Update localStorage and document class when theme changes
    useEffect(() => {
        const root = window.document.documentElement

        // Remove previous theme classes
        root.classList.remove('light', 'dark')

        // Add current theme class
        if (theme === 'system') {
            const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches
                ? 'dark'
                : 'light'
            root.classList.add(systemTheme)
        } else {
            root.classList.add(theme)
        }

        // Save theme preference
        localStorage.setItem(storageKey, theme)
    }, [theme, storageKey])

    // Listen for system theme changes
    useEffect(() => {
        if (theme === 'system') {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')

            const handleChange = () => {
                const root = window.document.documentElement
                const systemTheme = mediaQuery.matches ? 'dark' : 'light'

                root.classList.remove('light', 'dark')
                root.classList.add(systemTheme)
            }

            mediaQuery.addEventListener('change', handleChange)
            return () => mediaQuery.removeEventListener('change', handleChange)
        }
    }, [theme])

    // Provide theme context
    return (
        <ThemeContext.Provider value={{ theme, setTheme }} {...props}>
            {children}
        </ThemeContext.Provider>
    )
}
