/**
 * React Hook for Service Categories
 * Provides state management and data fetching for service categories
 * Follows the same pattern as other data hooks in the application
 */

import { useState, useEffect, useCallback } from 'react'
import { serviceCategoryService, ServiceCategory } from '../services/serviceCategoryService'

export interface UseServiceCategoriesState {
    serviceCategories: ServiceCategory[]
    loading: boolean
    error: string | null
    refetch: () => Promise<void>
    clearCache: () => Promise<void>
}

/**
 * Custom hook for managing service categories data
 */
export const useServiceCategories = (autoFetch: boolean = true): UseServiceCategoriesState => {
    const [serviceCategories, setServiceCategories] = useState<ServiceCategory[]>([])
    const [loading, setLoading] = useState<boolean>(false)
    const [error, setError] = useState<string | null>(null)

    /**
     * Fetch service categories from API
     */
    const fetchServiceCategories = useCallback(async (): Promise<void> => {
        try {
            setLoading(true)
            setError(null)

            console.log('[useServiceCategories] Fetching service categories')
            const categories = await serviceCategoryService.fetchServiceCategories()

            setServiceCategories(categories)
            console.log(
                `[useServiceCategories] Successfully loaded ${categories.length} service categories`,
            )
        } catch (err) {
            const errorMessage =
                err instanceof Error ? err.message : 'Failed to fetch service categories'
            console.error('[useServiceCategories] Error:', errorMessage)
            setError(errorMessage)

            // Keep existing data on error instead of clearing it
            // setServiceCategories([]);
        } finally {
            setLoading(false)
        }
    }, [])

    /**
     * Clear cache and refetch data
     */
    const clearCache = useCallback(async (): Promise<void> => {
        try {
            setLoading(true)
            setError(null)

            console.log('[useServiceCategories] Clearing cache and refetching')
            await serviceCategoryService.clearCache()
            await fetchServiceCategories()
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to clear cache'
            console.error('[useServiceCategories] Error clearing cache:', errorMessage)
            setError(errorMessage)
        } finally {
            setLoading(false)
        }
    }, [fetchServiceCategories])

    /**
     * Refetch service categories
     */
    const refetch = useCallback(async (): Promise<void> => {
        await fetchServiceCategories()
    }, [fetchServiceCategories])

    // Auto-fetch on mount if enabled
    useEffect(() => {
        if (autoFetch) {
            fetchServiceCategories()
        }
    }, [autoFetch, fetchServiceCategories])

    return {
        serviceCategories,
        loading,
        error,
        refetch,
        clearCache,
    }
}
