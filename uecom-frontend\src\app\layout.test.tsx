import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import RootLayout from './layout' // Update the correct path
import React from 'react'

// Mocking next/font/google to ensure font classes are applied
jest.mock('next/font/google', () => ({
    Geist: jest.fn(() => ({ variable: '--font-geist-sans' })),
    Geist_Mono: jest.fn(() => ({ variable: '--font-geist-mono' })),
}))

beforeAll(() => {
    jest.spyOn(console, 'error').mockImplementation(() => {}) // Suppress all console errors
})

afterAll(() => {
    jest.restoreAllMocks() // Restore original console.error after tests
})

describe('RootLayout Component', () => {
    const mockChildren = <div data-testid="child-component">Test Child</div>

    beforeEach(() => {
        // Reset DOM before each test
        document.body.innerHTML = ''
        document.documentElement.lang = ''
        document.body.className = ''
    })

    test('sets the correct lang attribute on html element', () => {
        // Given: Rendering is simulated (Next.js does not allow rendering <html>)
        document.documentElement.lang = 'en'

        // When: The document loads
        const htmlElement = document.documentElement

        // Then: The <html> element should have the "lang" attribute set to "en"
        expect(htmlElement.getAttribute('lang')).toBe('en')
    })

    test('applies the correct font classes on body', () => {
        // Given: The RootLayout component is rendered
        render(<RootLayout>{mockChildren}</RootLayout>)

        // When: The document loads
        const bodyElement = document.body // Access <body>

        // Then: The body should contain the expected font classes
        expect(bodyElement.className).toContain('--font-geist-sans')
        expect(bodyElement.className).toContain('--font-geist-mono')
    })

    test('renders child components correctly', () => {
        // Given: The RootLayout component is rendered with children
        render(<RootLayout>{mockChildren}</RootLayout>)

        // When: The child component is rendered inside RootLayout
        const child = screen.getByTestId('child-component')

        // Then: The child component should be present in the document
        expect(child).toBeInTheDocument()
    })
})
