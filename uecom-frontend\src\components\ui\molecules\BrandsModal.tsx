'use client'

import React from 'react'
import Modal from '../atoms/Modal'
import { useDeviceContext } from '../../../providers/DeviceProvider'

interface Brand {
    id: string
    label: string
    logoImage: string
}

interface BrandsModalProps {
    /**
     * Whether the modal is open
     */
    isOpen: boolean
    
    /**
     * Function to call when the modal should be closed
     */
    onClose: () => void
    
    /**
     * Array of brands to display
     */
    brands: Brand[]
    
    /**
     * Function to call when a brand is clicked
     */
    onBrandClick?: (brand: Brand) => void
}

/**
 * Modal component specifically for displaying all brands in a grid layout
 * Responsive design with different layouts for mobile, tablet, and desktop
 */
const BrandsModal: React.FC<BrandsModalProps> = ({
    isOpen,
    onClose,
    brands,
    onBrandClick,
}) => {
    const { isMobile, isTablet } = useDeviceContext()

    const handleBrandClick = (brand: Brand) => {
        if (onBrandClick) {
            onBrandClick(brand)
        }
        // Optionally close modal after brand selection
        // onClose()
    }

    // Determine grid columns based on device type
    const getGridCols = () => {
        if (isMobile) return 'grid-cols-3'
        if (isTablet) return 'grid-cols-4'
        return 'grid-cols-6'
    }

    // Determine modal size based on device type
    const getModalSize = () => {
        if (isMobile) return 'full'
        if (isTablet) return 'xl'
        return 'xl'
    }

    return (
        <Modal
            isOpen={isOpen}
            onClose={onClose}
            title="Our Brands"
            size={getModalSize()}
            className={isMobile ? 'mx-2' : ''}
        >
            <div className="p-4 sm:p-6">
                {/* Brand count info */}
                <div className="mb-4">
                    <p className="text-sm text-gray-600">
                        Showing {brands.length} brand{brands.length !== 1 ? 's' : ''}
                    </p>
                </div>

                {/* Brands Grid */}
                <div className={`grid ${getGridCols()} gap-3 sm:gap-4`}>
                    {brands.map((brand, index) => (
                        <button
                            key={`${brand.id}-${index}`}
                            onClick={() => handleBrandClick(brand)}
                            className="group relative bg-gray-50 hover:bg-gray-100 rounded-lg p-3 sm:p-4 
                                     flex items-center justify-center transition-all duration-200 
                                     hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 
                                     focus:ring-offset-2 border border-gray-200 hover:border-gray-300"
                            aria-label={`View services by ${brand.label}`}
                        >
                            {/* Brand Logo */}
                            <div className="w-full h-12 sm:h-16 flex items-center justify-center">
                                <img
                                    src={brand.logoImage}
                                    alt={`${brand.label} logo`}
                                    className="max-w-full max-h-full object-contain transition-transform 
                                             duration-200 group-hover:scale-105"
                                    loading="lazy"
                                />
                            </div>
                            
                            {/* Brand Name (visible on hover for larger screens) */}
                            <div className="absolute inset-x-0 bottom-0 bg-black/75 text-white text-xs 
                                          py-1 px-2 rounded-b-lg opacity-0 group-hover:opacity-100 
                                          transition-opacity duration-200 hidden sm:block">
                                <p className="text-center truncate">{brand.label}</p>
                            </div>
                            
                            {/* Brand Name (always visible on mobile) */}
                            <div className="absolute inset-x-0 bottom-0 bg-white/90 text-gray-800 
                                          text-xs py-1 px-1 rounded-b-lg sm:hidden">
                                <p className="text-center truncate font-medium">{brand.label}</p>
                            </div>
                        </button>
                    ))}
                </div>

                {/* Empty state */}
                {brands.length === 0 && (
                    <div className="text-center py-8">
                        <div className="text-gray-400 mb-2">
                            <svg
                                className="mx-auto h-12 w-12"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                                />
                            </svg>
                        </div>
                        <h3 className="text-lg font-medium text-gray-900 mb-1">No brands available</h3>
                        <p className="text-gray-500">Check back later for brand updates.</p>
                    </div>
                )}
            </div>
        </Modal>
    )
}

export default BrandsModal
